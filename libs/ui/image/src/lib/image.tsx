'use client';
/* eslint-disable jsx-a11y/alt-text */
import React, { useCallback } from 'react';
import Head from 'next/head';
import { imgBaseStyle, sizerBaseStyle, sizerImageStyle, wrapperBaseStyle } from './styles';
import {
  GenImgAttrsResult,
  ImageLoader,
  ImageLoaderProps,
  ImgElementStyle,
  LayoutValue,
  LoadingValue,
  OnLoadingComplete,
  PlaceholderValue,
  StaticImport,
} from './types';
import {
  defaultImageLoader,
  emptyDataUrl,
  generateImgAttrs,
  getInt,
  handleLoading,
  isStaticImport,
  isStaticRequire,
  loadedImageURLs,
  toBase64,
} from './utils';
import { useIntersection } from './useIntersection';
import styled from 'styled-components';

export type PreloadOptions = {
  media?: string;
};

export type FetchPriority = 'auto' | 'low' | 'high';

export type BzImageProps = Omit<
  JSX.IntrinsicElements['img'],
  'src' | 'srcSet' | 'ref' | 'width' | 'height' | 'loading' | 'style'
> & {
  src: string | StaticImport;
  width?: number | string;
  height?: number | string;
  crop?: string;
  trim?: string;
  fastly?: string;
  fetchPriority?: FetchPriority;
  layout?: LayoutValue;
  loader?: ImageLoader;
  loaderProps?: Partial<ImageLoaderProps>;
  // quality?: number | string;
  loading?: LoadingValue;
  lazyBoundary?: string;
  placeholder?: PlaceholderValue;
  preload?: boolean;
  preloadOptions?: PreloadOptions;
  blurDataURL?: string;
  blurOpacity?: number;
  objectFit?: ImgElementStyle['objectFit'];
  objectPosition?: ImgElementStyle['objectPosition'];
  onLoadingComplete?: OnLoadingComplete;
  wrapperClassName?: string;
};

export const BzImage = ({
  blurDataURL: blurDataUrlProp,
  blurOpacity = 20,
  className,
  crop,
  fastly,
  fetchPriority = 'low',
  height: heightProp,
  layout: layoutProp,
  lazyBoundary = '200px',
  loader = defaultImageLoader,
  loaderProps,
  loading = 'lazy',
  objectFit = 'contain',
  objectPosition,
  // quality,
  onLoadingComplete,
  placeholder = 'blur',
  preload = false,
  preloadOptions = {},
  sizes,
  src,
  trim,
  width: widthProp,
  wrapperClassName,
  ...all
}: BzImageProps) => {
  const rest: Partial<BzImageProps> = all;

  const layout: NonNullable<LayoutValue> = React.useMemo(
    () => layoutProp || (sizes ? 'responsive' : 'intrinsic'),
    [layoutProp, sizes],
  );

  const { blurDataURL, height, staticSrc, width } = React.useMemo(() => {
    let blurDataURL = blurDataUrlProp;
    let width = widthProp;
    let height = heightProp;
    let staticSrc = '';

    if (isStaticImport(src)) {
      const staticImageData = isStaticRequire(src) ? src.default : src;
      const throwError = (prop: string) => {
        throw new Error(
          `An object should only be passed to the image component src parameter if it comes from a static image import. It must include ${prop} and width. Received ${JSON.stringify(
            staticImageData,
          )}`,
        );
      };

      if (!staticImageData.src) {
        throwError('src');
      }

      blurDataURL = blurDataURL || staticImageData.blurDataURL;
      staticSrc = staticImageData.src;

      if (!layout || layout !== 'fill') {
        height = height || staticImageData.height;
        width = width || staticImageData.width;

        if (!staticImageData.height || !staticImageData.width) {
          throwError('width and height');
        }
      }
    }

    return {
      blurDataURL,
      height: getInt(height),
      staticSrc,
      width: getInt(width),
    };
  }, [blurDataUrlProp, heightProp, layout, src, widthProp]);

  const srcStr = React.useMemo(() => (typeof src === 'string' ? src : staticSrc), [src, staticSrc]);
  const adjustedLoading = preload ? 'eager' : loading || 'lazy';

  const sizerSvg = React.useMemo(
    () => `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg" version="1.1"/>`,
    [height, width],
  );
  // const qualityInt = getInt(quality);

  const isLazy = React.useMemo(() => {
    if (srcStr.startsWith('data:') || srcStr.startsWith('blob:')) {
      return false;
    }

    if (typeof window !== 'undefined' && loadedImageURLs.has(srcStr)) {
      return false;
    }

    return adjustedLoading === 'lazy';
  }, [adjustedLoading, srcStr]);

  const [setRef, isIntersected] = useIntersection<HTMLImageElement>({
    disabled: !isLazy,
    rootMargin: lazyBoundary,
  });

  const isVisible = React.useMemo(() => !isLazy || isIntersected, [isLazy, isIntersected]);

  const imgStyle: ImgElementStyle = React.useMemo(
    () => ({
      ...imgBaseStyle,
      objectFit,
      objectPosition,
    }),
    [objectFit, objectPosition],
  );

  const blurStyle = React.useMemo(
    () =>
      placeholder === 'blur' && blurDataURL && !preload
        ? {
            backgroundImage: `url("${blurDataURL}")`,
            backgroundPosition: objectPosition || '0% 0%',
            backgroundSize: objectFit || 'cover',
            filter: 'blur(20px)',
          }
        : {},
    [blurDataURL, placeholder, preload, objectPosition, objectFit],
  );

  const { hasSizer, sizerStyle, wrapperStyle } = React.useMemo<{
    hasSizer: boolean;
    sizerStyle: ImgElementStyle;
    wrapperStyle: ImgElementStyle;
  }>(() => {
    const wrapperStyle: ImgElementStyle = {
      ...wrapperBaseStyle,
    };

    const sizerStyle: ImgElementStyle = {
      ...sizerBaseStyle,
    };

    if (layout === 'fill') {
      // <Image src="i.png" layout="fill" />
      wrapperStyle.display = 'block';
      wrapperStyle.position = 'absolute';
      wrapperStyle.top = 0;
      wrapperStyle.left = 0;
      wrapperStyle.bottom = 0;
      wrapperStyle.right = 0;

      return {
        hasSizer: false,
        sizerStyle,
        wrapperStyle,
      };
    } else if (typeof width !== 'undefined' && typeof height !== 'undefined') {
      // <Image src="i.png" width="100" height="100" />
      const quotient = height / width;
      const paddingTop = isNaN(quotient) ? '100%' : `${quotient * 100}%`;

      if (layout === 'responsive') {
        // <Image src="i.png" width="100" height="100" layout="responsive" />
        wrapperStyle.display = 'block';
        wrapperStyle.position = 'relative';
        sizerStyle.paddingTop = paddingTop;

        return {
          hasSizer: true,
          sizerStyle,
          wrapperStyle,
        };
      } else if (layout === 'intrinsic') {
        // <Image src="i.png" width="100" height="100" layout="intrinsic" />
        wrapperStyle.display = 'inline-block';
        wrapperStyle.position = 'relative';
        wrapperStyle.maxWidth = '100%';
        sizerStyle.maxWidth = '100%';

        return {
          hasSizer: true,
          sizerStyle,
          wrapperStyle,
        };
      } else if (layout === 'fixed') {
        // <Image src="i.png" width="100" height="100" layout="fixed" />
        wrapperStyle.display = 'inline-block';
        wrapperStyle.position = 'relative';
        wrapperStyle.width = width;
        wrapperStyle.height = height;

        return {
          hasSizer: false,
          sizerStyle,
          wrapperStyle,
        };
      }
    } else {
      // <Image src="i.png" />
      if (process.env.NODE_ENV !== 'production') {
        throw new Error(
          `Image with src "${src}" must use "width" and "height" properties or "layout='fill'" property.`,
        );
      }
    }

    return {
      hasSizer: false,
      sizerStyle: {},
      wrapperStyle: {},
    };
  }, [height, layout, src, width]);

  const generatedImgAttrs = React.useMemo(() => {
    return generateImgAttrs({
      crop,
      fastly,
      fetchpriority: fetchPriority,
      layout,
      loader,
      loaderProps,
      sizes,
      src: srcStr,
      // quality: qualityInt,
      trim,
      width,
    });
  }, [crop, fastly, fetchPriority, layout, loader, loaderProps, sizes, srcStr, trim, width]);

  const imgAttributes: GenImgAttrsResult = React.useMemo(() => {
    return isVisible
      ? generatedImgAttrs
      : {
          sizes: undefined,
          srcSet: undefined,
          // eslint-disable-next-line sort-keys-fix/sort-keys-fix
          src: emptyDataUrl,
        };
  }, [generatedImgAttrs, isVisible]);

  const handleRef = useCallback(
    (img: HTMLImageElement) => {
      setRef(img);
      handleLoading(img, srcStr, layout, placeholder, onLoadingComplete);
    },
    [layout, onLoadingComplete, placeholder, setRef, srcStr],
  );

  const shouldPreload = preload && typeof srcStr === 'string' && !loadedImageURLs.has(srcStr);

  return (
    <>
      <Head>
        {blurDataURL && <link as="image" href={blurDataURL} key={blurDataURL} rel="preload" />}
        {shouldPreload && (
          <link
            as="image"
            fetchPriority={fetchPriority}
            href={generatedImgAttrs.src}
            imageSizes={generatedImgAttrs.sizes}
            imageSrcSet={generatedImgAttrs.srcSet}
            key={generatedImgAttrs.src}
            media={preloadOptions?.media}
            rel="preload"
          />
        )}
      </Head>
      <span className={wrapperClassName} style={wrapperStyle}>
        {blurDataURL && <BlurPlaceholder $opacity={blurOpacity} $url={blurDataURL} role="img" />}
        {hasSizer ? (
          <span style={sizerStyle}>
            {sizerSvg ? (
              <img
                alt=""
                aria-hidden={true}
                src={`data:image/svg+xml;base64,${toBase64(sizerSvg)}`}
                style={sizerImageStyle}
              />
            ) : null}
          </span>
        ) : null}
        <img
          {...rest}
          {...imgAttributes}
          className={className}
          decoding={isLazy ? 'async' : 'sync'}
          loading={adjustedLoading}
          ref={handleRef}
          style={{ ...imgStyle, ...blurStyle }}
        />
        <noscript>
          <img
            {...rest}
            {...generatedImgAttrs}
            className={className}
            decoding="async"
            loading={adjustedLoading}
            style={imgStyle}
          />
        </noscript>
      </span>
    </>
  );
};

const BlurPlaceholder = styled.div<{ $url: string; $opacity?: number }>`
  background: url(${({ $url }) => $url});
  position: absolute;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  filter: blur(${({ $opacity }) => `${$opacity || 20}px`});
`;
