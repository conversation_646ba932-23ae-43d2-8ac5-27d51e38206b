import { useCallback, useEffect, useState, useRef, startTransition } from 'react';
import { ArticleData } from '@benzinga/article-manager';
import { IsUserPaywalledReturn, useIsUserPaywalled } from '@benzinga/user-context';
import { getPaywallType } from '../utils';
import { DeviceType } from '@benzinga/device-utils';
import { sophiManager } from '@benzinga/ads-utils';

interface UseArticlePaywallStatusReturn {
  isNotPaywalled: boolean;
  isPaywallActive: boolean;
  setIsPaywallActive: (value: boolean) => void;
  paywall: IsUserPaywalledReturn;
  headerRef: (node: HTMLDivElement | null) => void;
}

export const useArticlePaywallStatus = (
  articleData: ArticleData,
  disablePaywall?: boolean,
  deviceType?: DeviceType | null,
): UseArticlePaywallStatusReturn => {
  const isChanneled = useCallback(
    (tids: number[]) => {
      return articleData?.channels?.some(channel => tids.includes(channel?.tid));
    },
    [articleData?.channels],
  );

  const isNotPaywalled = isChanneled([165347]);
  const paywall = useIsUserPaywalled('com/read', 'unlimited-articles', getPaywallType(articleData), true);

  const [isPaywallActive, setIsPaywallActive] = useState(false);
  const headerRef = useRef<HTMLDivElement | null>(null);
  const scrollHandlerRef = useRef<(() => void) | null>(null);

  const setHeaderRef = useCallback((node: HTMLDivElement | null) => {
    if (node) {
      headerRef.current = node;
    }
  }, []);

  useEffect(() => {
    if (disablePaywall) {
      setIsPaywallActive(false);
      return;
    }

    if (!headerRef.current || !paywall.active) return;

    const handleScroll = () => {
      if (!headerRef.current) return;

      const navHeader = document.getElementById('navigation-header');
      const navHeaderHeight = navHeader?.offsetHeight ?? 0;

      const headerRect = headerRef.current.getBoundingClientRect();
      const headerHeight = headerRect.height;
      const scrollPosition = window.scrollY;
      const scrollThreshold = headerHeight * 0.4 + (deviceType === 'mobile' ? 0 : navHeaderHeight);

      if (scrollPosition >= scrollThreshold) {
        startTransition(() => {
          setIsPaywallActive(true);
          const section = articleData?.channels?.[0]?.name || '';
          sophiManager.trackWallHit('paywall', section);
        });
        if (scrollHandlerRef.current) {
          window.removeEventListener('scroll', scrollHandlerRef.current);
        }
      }
    };

    scrollHandlerRef.current = handleScroll;
    window.addEventListener('scroll', scrollHandlerRef.current);

    return () => {
      if (scrollHandlerRef.current) {
        window.removeEventListener('scroll', scrollHandlerRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [disablePaywall, paywall.active]);

  return {
    headerRef: setHeaderRef,
    isNotPaywalled,
    isPaywallActive,
    paywall,
    setIsPaywallActive,
  };
};
