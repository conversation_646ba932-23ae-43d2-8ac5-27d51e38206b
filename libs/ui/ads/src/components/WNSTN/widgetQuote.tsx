import React from 'react';

import { WidgetType, WNSTNWidget } from './widget';
import { getCRYPTOQuestions, getQuoteQuestions, getRandomQuestions } from './utils';
import { DEFAULT_QUESTIONS } from './widget';
import { NoFirstRender } from '@benzinga/hooks';
import { DelayedQuote } from '@benzinga/quotes-manager';

const FloatingWNSTNWidget = React.lazy(() =>
  import('./floatingWidget').then(module => ({ default: module.FloatingWNSTNWidget })),
);

interface Props {
  symbol?: string;
  richQuoteData?: DelayedQuote;
}

export const WNSTNWidgetQuote: React.FC<Props> = ({ richQuoteData, symbol }) => {
  const assetType = React.useMemo(() => {
    return richQuoteData?.type || 'STOCK';
  }, [richQuoteData?.type]);

  const randomQuestions = React.useMemo(() => {
    if (!symbol) {
      return DEFAULT_QUESTIONS;
    }

    const questions = assetType === 'CRYPTO' ? getCRYPTOQuestions(symbol) : getQuoteQuestions(symbol);

    return getRandomQuestions(questions, 5);
  }, [symbol, assetType]);

  return (
    <React.Suspense fallback={<div />}>
      <NoFirstRender>
        <FloatingWNSTNWidget
          assetType={assetType}
          questions={randomQuestions}
          symbol={symbol}
          widgetType={WidgetType.Asset}
        />
        {/* <WNSTNWidget questions={randomQuestions} small title={'Got Questions? Ask'} widgetType={WidgetType.Asset} /> */}
      </NoFirstRender>
    </React.Suspense>
  );
};
