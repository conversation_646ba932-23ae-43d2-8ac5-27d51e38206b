import React from 'react';

import { BlockComponentProps, Block } from '@benzinga/blocks';
import { WordpressPost } from '@benzinga/content-manager';

const AltOfferingFeedbackBlock = React.lazy(() =>
  import('./Blocks/AltOfferingFeedbackBlock').then(module => ({ default: module.AltOfferingFeedbackBlock })),
);
const AnalystRatingsBlock = React.lazy(() =>
  import('./Blocks/AnalystRatingsBlock').then(module => ({ default: module.AnalystRatingsBlock })),
);
const ArticleGroupsBlock = React.lazy(() =>
  import('./Blocks/ArticleGroupsBlock').then(module => ({ default: module.ArticleGroupsBlock })),
);
const AskTheExpert = React.lazy(() =>
  import('./Blocks/AskTheExpert').then(module => ({ default: module.AskTheExpert })),
);
const BasicInfoCardsBlock = React.lazy(() =>
  import('./Blocks/BasicInfoCardsBlock').then(module => ({ default: module.BasicInfoCardsBlock })),
);
const CalculatorsBlock = React.lazy(() =>
  import('./Blocks/CalculatorsBlock').then(module => ({ default: module.CalculatorsBlock })),
);
const CapitalExpenseCalculatorBlock = React.lazy(() =>
  import('./Blocks/CapitalExpenseCalculatorBlock').then(module => ({ default: module.CapitalExpenseCalculatorBlock })),
);
const CapitalRateCalculatorBlock = React.lazy(() =>
  import('./Blocks/CapitalRateCalculatorBlock').then(module => ({ default: module.CapitalRateCalculatorBlock })),
);
const CardWithIconBlock = React.lazy(() =>
  import('./Blocks/CardWithIconBlock').then(module => ({ default: module.CardWithIconBlock })),
);
const CompareProductsSideBySideBlock = React.lazy(() =>
  import('./Blocks/CompareProductsSideBySideBlock').then(module => ({
    default: module.CompareProductsSideBySideBlock,
  })),
);
const CoveyAnalystMetricsBlock = React.lazy(() =>
  import('./Blocks/CoveyAnalystMetricsBlock').then(module => ({ default: module.CoveyAnalystMetricsBlock })),
);
const CryptoCardBlock = React.lazy(() =>
  import('./Blocks/CryptoCardBlock').then(module => ({ default: module.CryptoCardBlock })),
);
const CryptoPunkBlock = React.lazy(() =>
  import('./Blocks/CryptoPunkBlock').then(module => ({ default: module.CryptoPunkBlock })),
);
const EntitiesBlock = React.lazy(() =>
  import('./Blocks/EntitiesBlock').then(module => ({ default: module.EntitiesBlock })),
);
const FAQBlock = React.lazy(() => import('./Blocks/FAQBlock').then(module => ({ default: module.FAQBlock })));
const FeaturedArticles = React.lazy(() =>
  import('./Blocks/FeaturedArticles').then(module => ({ default: module.FeaturedArticles })),
);
const FeaturedCoinsBlock = React.lazy(() =>
  import('./Blocks/FeaturedCoinsBlock').then(module => ({ default: module.FeaturedCoinsBlock })),
);
const FeaturedProductsBlock = React.lazy(() =>
  import('./Blocks/FeaturedProductsBlock').then(module => ({ default: module.FeaturedProductsBlock })),
);
const FinancialAdvisorInfoCardBlock = React.lazy(() =>
  import('./Blocks/FinancialAdvisorInfoCardBlock').then(module => ({ default: module.FinancialAdvisorInfoCardBlock })),
);
const GalleryBlock = React.lazy(() =>
  import('./Blocks/GalleryBlock').then(module => ({ default: module.GalleryBlock })),
);
const GetInsuranceQuotesBlock = React.lazy(() =>
  import('./Blocks/GetInsuranceQuotesBlock').then(module => ({ default: module.GetInsuranceQuotesBlock })),
);
const HeaderCallToAction = React.lazy(() =>
  import('./Blocks/HeaderCallToAction').then(module => ({ default: module.HeaderCallToAction })),
);
const HowToBlock = React.lazy(() => import('./Blocks/HowToBlock').then(module => ({ default: module.HowToBlock })));
const LemonadeCompensationCalculatorBlock = React.lazy(() =>
  import('./Blocks/LemonadeCompensationCalculatorBlock').then(module => ({
    default: module.LemonadeCompensationCalculatorBlock,
  })),
);
const MoneyLayoutHeaderBlock = React.lazy(() =>
  import('./Blocks/MoneyLayoutHeaderBlock').then(module => ({ default: module.MoneyLayoutHeaderBlock })),
);
const MoneyPostsBlock = React.lazy(() =>
  import('./Blocks/MoneyPostsBlock').then(module => ({ default: module.MoneyPostsBlock })),
);
const MoneyServicesListBlock = React.lazy(() =>
  import('./Blocks/MoneyServicesListBlock').then(module => ({ default: module.MoneyServicesListBlock })),
);
const MortgageCalculatorBlock = React.lazy(() =>
  import('./Blocks/MortgageCalculatorBlock').then(module => ({ default: module.MortgageCalculatorBlock })),
);
const MortgageLoanCompareBlock = React.lazy(() =>
  import('./Blocks/MortgageLoanCompareBlock').then(module => ({ default: module.MortgageLoanCompareBlock })),
);
const NewsCardsBlock = React.lazy(() =>
  import('./Blocks/NewsCardsBlock').then(module => ({ default: module.NewsCardsBlock })),
);
const NewsListGroupBlock = React.lazy(() =>
  import('./Blocks/NewsListGroupBlock').then(module => ({ default: module.NewsListGroupBlock })),
);
const NewsListWithPaginationBlock = React.lazy(() =>
  import('./Blocks/NewsListWithPaginationBlock').then(module => ({ default: module.NewsListWithPaginationBlock })),
);
const NewsWithTabsBlock = React.lazy(() =>
  import('./Blocks/NewsWithTabsBlock').then(module => ({ default: module.NewsWithTabsBlock })),
);
const OptionsProfitCalculatorBlock = React.lazy(() =>
  import('./Blocks/OptionsProfitCalculatorBlock').then(module => ({ default: module.OptionsProfitCalculatorBlock })),
);
const PlaceholderBlock = React.lazy(() =>
  import('./Blocks/PlaceholderBlock').then(module => ({ default: module.PlaceholderBlock })),
);
const PollBlock = React.lazy(() => import('./Blocks/PollBlock').then(module => ({ default: module.PollBlock })));
const ProductCardsBlock = React.lazy(() =>
  import('./Blocks/ProductCardsBlock').then(module => ({ default: module.ProductCardsBlock })),
);
const ProductReviewCard = React.lazy(() =>
  import('./Blocks/ProductReviewCard').then(module => ({ default: module.ProductReviewCard })),
);
const ProductsListBlock = React.lazy(() =>
  import('./Blocks/ProductsListBlock').then(module => ({ default: module.ProductsListBlock })),
);
const ReviewEntriesBlock = React.lazy(() =>
  import('./Blocks/ReviewEntriesBlock').then(module => ({ default: module.ReviewEntriesBlock })),
);
const QuotesBlock = React.lazy(() => import('./Blocks/QuotesBlock').then(module => ({ default: module.QuotesBlock })));
const QuicklinksBlock = React.lazy(() =>
  import('./Blocks/QuicklinksBlock').then(module => ({ default: module.QuicklinksBlock })),
);
const RatingsTableBlock = React.lazy(() =>
  import('./Blocks/RatingsTableBlock').then(module => ({ default: module.RatingsTableBlock })),
);
const ReviewCardsBlock = React.lazy(() =>
  import('./Blocks/ReviewCardsBlock').then(module => ({ default: module.ReviewCardsBlock })),
);
const ReviewedByBenzingaBlock = React.lazy(() =>
  import('./Blocks/ReviewedByBenzingaBlock').then(module => ({ default: module.ReviewedByBenzingaBlock })),
);
const StandOutAreaBlock = React.lazy(() =>
  import('./Blocks/StandOutAreaBlock').then(module => ({ default: module.StandOutAreaBlock })),
);
const StarRatingBlock = React.lazy(() =>
  import('./Blocks/StarRatingBlock').then(module => ({ default: module.StarRatingBlock })),
);
const SubmitReviewBlock = React.lazy(() =>
  import('./Blocks/SubmitReviewBlock').then(module => ({ default: module.SubmitReviewBlock })),
);
const SuggestedEntitiesBlock = React.lazy(() =>
  import('./Blocks/SuggestedEntitiesBlock').then(module => ({ default: module.SuggestedEntitiesBlock })),
);
const TableBlock = React.lazy(() => import('./Blocks/Table').then(module => ({ default: module.TableBlock })));
const TableOfContents = React.lazy(() =>
  import('./TableOfContents').then(module => ({ default: module.TableOfContents })),
);
const TeamCardsBlock = React.lazy(() =>
  import('./Blocks/TeamCardsBlock').then(module => ({ default: module.TeamCardsBlock })),
);
const TestimonialCardBlock = React.lazy(() =>
  import('./Blocks/TestimonialCardBlock').then(module => ({ default: module.TestimonialCardBlock })),
);
const TickerBoxBlock = React.lazy(() =>
  import('./Blocks/TickerBoxBlock').then(module => ({ default: module.TickerBoxBlock })),
);
const QuotesCardBlock = React.lazy(() =>
  import('./Blocks/QuotesCardBlock').then(module => ({ default: module.QuotesCardBlock })),
);
const TickerCardCarouselBlock = React.lazy(() =>
  import('./Blocks/TickerCardCarouselBlock').then(module => ({ default: module.TickerCardCarouselBlock })),
);
const TipCardsBlock = React.lazy(() =>
  import('./Blocks/TipCardsBlock').then(module => ({ default: module.TipCardsBlock })),
);
const TradingViewWidgetBlock = React.lazy(() =>
  import('./Blocks/TradingViewWidgetBlock').then(module => ({ default: module.TradingViewWidgetBlock })),
);
const TransparentlyBlock = React.lazy(() =>
  import('./Blocks/TransparentlyBlock').then(module => ({ default: module.TransparentlyBlock })),
);
const DentalInsuranceBlock = React.lazy(() =>
  import('./Blocks/DentalInsuranceBlock').then(module => ({ default: module.DentalInsuranceBlock })),
);
const BZLogoSearch = React.lazy(() =>
  import('./Blocks/BZLogoSearch').then(module => ({ default: module.BZLogoSearch })),
);
const WorkersCompensationDataFormBlock = React.lazy(() =>
  import('./Blocks/WorkersCompensationDataFormBlock').then(module => ({
    default: module.WorkersCompensationDataFormBlock,
  })),
);
const FuturesBlock = React.lazy(() =>
  import('./Blocks/FuturesBlock').then(module => ({ default: module.FuturesBlock })),
);

const MediaTextBlock = React.lazy(() => import('./Blocks/MediaTextBlock'));

const LinksWithIconBlock = React.lazy(() =>
  import('./Blocks/LinksWithIconBlock').then(module => ({ default: module.LinksWithIconBlock })),
);

const BudgetCalculatorBlock = React.lazy(() =>
  import('./Blocks/BudgetCalculatorBlock').then(module => ({ default: module.BudgetCalculatorBlock })),
);

const HYSACalculatorBlock = React.lazy(() =>
  import('./Blocks/HYSACalculatorBlock').then(module => ({ default: module.HYSACalculatorBlock })),
);

const CoinExchangeRateCalculatorBlock = React.lazy(() =>
  import('./Blocks/CoinExchangeRateCalculatorBlock').then(module => ({
    default: module.CoinExchangeRateCalculatorBlock,
  })),
);

const MoneyAuthorBioBlock = React.lazy(() =>
  import('./Blocks/MoneyAuthorBioBlock').then(module => ({ default: module.MoneyAuthorBioBlock })),
);

const MavenMarketingBlock = React.lazy(() =>
  import('./Blocks/MarvenMarketingBlock').then(module => ({ default: module.MarvenMarketingBlock })),
);
const WebinarBannerAd = React.lazy(() => import('@benzinga/ads').then(module => ({ default: module.WebinarBannerAd })));
const CommentBlock = React.lazy(() =>
  import('./Blocks/CommentBlock').then(module => ({ default: module.CommentBlock })),
);
const HELOCCalculatorBlock = React.lazy(() =>
  import('./Blocks/HELOCCalculatorBlock').then(module => ({ default: module.HELOCCalculatorBlock })),
);

export interface Block {
  blockName: string;
}

export interface MoneyBlockProps extends BlockComponentProps {
  post?: WordpressPost | null;
}

export const MoneyBlock: React.FC<MoneyBlockProps> = props => {
  props.block.type = props.type ?? 'default';
  const block = getMoneyBlock(props);
  return block ? block : <Block {...props} getBlock={getMoneyBlock} />;
};

const getMoneyBlock = ({ block, campaigns, post }: MoneyBlockProps) => {
  if (block.blockName === 'ad/campaign') {
    return <div className="bz-campaign" dangerouslySetInnerHTML={{ __html: block.value }} />;
  } else if (block.blockName === 'acf/cta-header') {
    return <HeaderCallToAction {...block.attrs.data} />;
  } else if (block.blockName === 'advanced-gutenberg-blocks/summary' || block.blockName === 'acf/table-of-contents') {
    return <TableOfContents items={block.headers} />;
  } else if (block.blockName === 'yoast/how-to-block') {
    return <HowToBlock block={block} post={post} />;
  } else if (block.blockName === 'acf/featured-articles') {
    return <FeaturedArticles articles={block.articles || block.attrs.data?.articles} attrs={block.attrs} />;
  } else if (block.blockName === 'acf/article-groups') {
    return <ArticleGroupsBlock {...block} />;
  } else if (block.blockName === 'acf/stock-quotes') {
    return <QuotesBlock {...block} />;
  } else if (block.blockName === 'acf/stock-quotes-cards') {
    return <QuotesCardBlock {...block} />;
  } else if (block.blockName === 'acf/stocks-list-container') {
    block.attrs.layout = 'stock-list';
    return <QuotesBlock {...block} />;
  } else if (block.blockName === 'acf/quicklinks') {
    return <QuicklinksBlock {...block} />;
  } else if (block.blockName === 'acf/recent-reviews') {
    return <ReviewCardsBlock {...block} />;
  } else if (block.blockName === 'acf/financial-advisor-information-card') {
    return <FinancialAdvisorInfoCardBlock {...block} />;
  } else if (block.blockName === 'acf/ratings-table') {
    return <RatingsTableBlock {...block} />;
  } else if (block.blockName === 'acf/get-insurance-quote') {
    return <GetInsuranceQuotesBlock {...block} />;
  } else if (block.blockName === 'acf/get-financial-quote') {
    return <GetInsuranceQuotesBlock {...block} />;
  } else if (block.blockName === 'acf/mortage-calculator') {
    return <MortgageCalculatorBlock {...block} />;
  } else if (block.blockName === 'acf/heloc-calculator') {
    return <HELOCCalculatorBlock {...block?.attrs?.data} />;
  } else if (block.blockName === 'acf/mortgage-loan-compare') {
    return <MortgageLoanCompareBlock block={block} />;
  } else if (block.blockName === 'acf/money-posts') {
    return <MoneyPostsBlock {...block} />;
  } else if (block.blockName === 'acf/top-analyst-ratings') {
    return <AnalystRatingsBlock {...block} />;
  } else if (block.blockName === 'acf/featured-reviews') {
    return <ReviewCardsBlock {...block} reviews={block.attrs.data.featured_reviews} />;
  } else if ((block.blockName === 'acf/cryptocurrency-card' || block.blockName === 'acf/quote-card') && block.coin) {
    return <CryptoCardBlock {...block} />;
    // } else if (block.blockName === 'acf/page-sidebar-placement') {
    //   return post ? post.sidebar_placement.blocks?.map((widget_block, i) => {
    //     return <MoneyBlock block={widget_block} campaigns={campaigns} key={i} />;
    //   }) : <div/>;
  } else if (block.blockName === 'acf/featured-coins') {
    return <FeaturedCoinsBlock {...block} />;
  } else if (block.blockName === 'acf/product-review-card') {
    return <ProductReviewCard {...block} />;
  } else if (block.blockName === 'custom/offering-feedback') {
    return <AltOfferingFeedbackBlock {...block} />;
  } else if (block.blockName === 'acf/featured-products') {
    return <FeaturedProductsBlock {...block} />;
  } else if (block.blockName === 'acf/product-cards') {
    return <ProductCardsBlock {...block} />;
  } else if (block.blockName === 'acf/products-list') {
    return <ProductsListBlock {...block} />;
  } else if (block.blockName === 'acf/tip-cards') {
    return <TipCardsBlock {...block} />;
  } else if (block.blockName === 'acf/testimonial') {
    return <TestimonialCardBlock {...block} />;
  } else if (block.blockName === 'acf/team') {
    return <TeamCardsBlock {...block} />;
  } else if (block.blockName === 'acf/compare-products-side-by-side') {
    return <CompareProductsSideBySideBlock {...block} />;
  } else if (block.blockName === 'acf/suggested-entities') {
    return <SuggestedEntitiesBlock block={block} />;
  } else if (block.blockName === 'acf/trading-view') {
    return <TradingViewWidgetBlock {...block} />;
  } else if (block.blockName === 'acf/news-list-group') {
    return <NewsListGroupBlock {...block} />;
  } else if (block.blockName === 'acf/entities') {
    return <EntitiesBlock block={block} />;
  } else if (block.blockName === 'acf/submit-review') {
    return <SubmitReviewBlock {...block} />;
  } else if (block.blockName === 'acf/review-entries') {
    return <ReviewEntriesBlock {...block} />;
  } else if (block.blockName === 'acf/bz-crypto-punk') {
    return <CryptoPunkBlock {...block} />;
  } else if (block.blockName === 'acf/table') {
    return <TableBlock {...block} />;
  } else if (block.blockName === 'acf/star-rating') {
    return <StarRatingBlock {...block} />;
  } else if (block.blockName === 'acf/stand-out-card') {
    return <StandOutAreaBlock {...block} />;
  } else if (block.blockName === 'acf/image-gallery' || block.blockName === 'core/gallery') {
    return <GalleryBlock {...block} />;
  } else if (block.blockName === 'acf/ticker-box') {
    return <TickerBoxBlock {...block} />;
  } else if (block.blockName === 'acf/ticker-card-carousel') {
    return <TickerCardCarouselBlock {...block} />;
  } else if (block.blockName === 'acf/faq') {
    return <FAQBlock {...block} />;
  } else if (block.blockName === 'acf/capital-rate-calculator') {
    return <CapitalRateCalculatorBlock />;
  } else if (block.blockName === 'acf/capital-expense-calculator') {
    return <CapitalExpenseCalculatorBlock />;
  } else if (block.blockName === 'acf/options-profit-calculator') {
    return <OptionsProfitCalculatorBlock />;
  } else if (block.blockName === 'acf/poll') {
    return <PollBlock {...block.attrs.data} />;
  } else if (block.blockName === 'acf/transparently') {
    return <TransparentlyBlock {...block} post={post} />;
  } else if (block.blockName === 'acf/cta-placeholder') {
    return <PlaceholderBlock {...block} campaigns={campaigns} getBlock={getMoneyBlock} />;
    // } else if (block.blockName === 'acf/featured-articles') {
  } else if (block.blockName === 'acf/lemonad-compensation-data-form') {
    return <LemonadeCompensationCalculatorBlock {...block} />;
  } else if (block.blockName === 'acf/workers-compensation-data-form') {
    return <WorkersCompensationDataFormBlock {...block} />;
  } else if (block.blockName === 'acf/ask-the-expert') {
    return <AskTheExpert {...block} />;
  } else if (block.blockName === 'acf/covey-analyst-metrics') {
    return <CoveyAnalystMetricsBlock {...block} />;
  } else if (block.blockName === 'acf/calculators') {
    return <CalculatorsBlock {...block} />;
  } else if (block.blockName === 'core/media-text') {
    return <MediaTextBlock block={block} />;
  } else if (block.blockName === 'acf/news-with-tabs') {
    return <NewsWithTabsBlock {...block} />;
  } else if (block.blockName === 'acf/news-cards-list') {
    return <NewsCardsBlock {...block} />;
  } else if (block.blockName === 'acf/news-list-with-pagination') {
    return <NewsListWithPaginationBlock {...block} />;
  } else if (block.blockName === 'acf/basic-info-cards') {
    return <BasicInfoCardsBlock {...block} />;
  } else if (block.blockName === 'acf/reviewed-by-benzinga') {
    return <ReviewedByBenzingaBlock {...block} />;
  } else if (block.blockName === 'acf/money-services-list') {
    return <MoneyServicesListBlock {...block} />;
  } else if (block.blockName === 'acf/money-layout-header') {
    return <MoneyLayoutHeaderBlock {...block} />;
  } else if (block.blockName === 'acf/icon-with-card-block') {
    return <CardWithIconBlock {...block} />;
  } else if (block.blockName === 'acf/dental-insruance') {
    return <DentalInsuranceBlock {...block} />;
  } else if (block.blockName === 'acf/bz-logo-search') {
    return <BZLogoSearch {...block} />;
  } else if (block.blockName === 'acf/links-with-icon') {
    return <LinksWithIconBlock {...block} />;
  } else if (block.blockName === 'acf/future-commoditites') {
    return <FuturesBlock {...block} />;
  } else if (block.blockName === 'acf/budget-calculator') {
    return <BudgetCalculatorBlock {...block} />;
  } else if (block.blockName === 'money-author-bio') {
    return <MoneyAuthorBioBlock {...block} />;
  } else if (block.blockName === 'acf/hysa-calculator') {
    return <HYSACalculatorBlock {...block} />;
  } else if (block.blockName === 'acf/maven-marketing') {
    return <MavenMarketingBlock {...block} />;
  } else if (block.blockName === 'acf/maven-webinar-banner') {
    return <WebinarBannerAd {...block} />;
  } else if (block.blockName === 'acf/coin-exchange-rate') {
    return <CoinExchangeRateCalculatorBlock {...block} />;
  } else if (block.blockName === 'acf/comment') {
    return <CommentBlock {...block} />;
  }
  return null;
};
