import React from 'react';
import styled from '@benzinga/themetron';
import { TradingViewWidget } from '@benzinga/charts-ui';

type ChartRanges = '1D' | '5D' | '1M' | '3M' | '6M' | '12M' | 'YTD' | 'ALL';

export interface TradingViewWidgetBlockProps {
  attrs: {
    data: {
      company_exchange: string;
      company_symbol: string;
      range?: ChartRanges;
    };
  };
}

export const TradingViewWidgetBlock: React.FC<TradingViewWidgetBlockProps> = ({ attrs }) => {
  return (
    <TradingViewWidgetBlockWrapper>
      <React.Suspense>
        {attrs?.data?.company_symbol && (
          <TradingViewWidget
            widgetProps={{
              allow_symbol_change: false,
              backgroundColor: '#F2F8FF',
              height: 400,
              hide_side_toolbar: true,
              hide_top_toolbar: true,
              hide_volume: true,
              range: attrs?.data?.range ?? '1D',
              style: 3,
              symbol: `${attrs?.data?.company_exchange}:${attrs?.data?.company_symbol}`,
              theme: 'light',
              withdateranges: false,
            }}
          />
        )}
      </React.Suspense>
    </TradingViewWidgetBlockWrapper>
  );
};

export const TradingViewWidgetBlockWrapper = styled.div`
  margin: 2rem 0;
`;
