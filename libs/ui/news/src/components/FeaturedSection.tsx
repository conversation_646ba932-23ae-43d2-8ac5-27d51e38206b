import React from 'react';
import { FeaturedCard } from './FeaturedCard';
import styled from '@benzinga/themetron';
import { News, StoryObject } from '@benzinga/basic-news-manager';
import type { DeviceType } from '@benzinga/device-utils';

interface FeaturedSectionProps {
  nodes: StoryObject[] | News[];
  deviceType?: DeviceType;
}

export const FeaturedSection: React.FC<FeaturedSectionProps> = ({ deviceType, nodes }) => {
  const showSecondary = nodes.length > 1;
  if (!nodes.length) return <div />;

  const isMobile = deviceType === 'mobile';
  return (
    <FeaturedSectionWrapper className="top-section-container">
      <div className="top-story primary-story">
        <FeaturedCard
          fetchPriority="high"
          imageWidth={isMobile ? 285 : 630}
          layout="hero"
          loading="eager"
          node={nodes[0]}
          relativePath={true}
        />
      </div>
      {showSecondary ? (
        <div className="secondary-stories">
          <div className="top-story secondary-story desktop">
            {nodes[1] && (
              <FeaturedCard
                fetchPriority="high"
                imageWidth={240}
                layout="hero"
                loading="eager"
                node={nodes[1]}
                preloadOptions={{ media: '(min-width: 601px)' }}
                relativePath={true}
                size="compact"
              />
            )}
            {nodes[2] && (
              <FeaturedCard
                fetchPriority="high"
                imageWidth={240}
                layout="hero"
                loading="eager"
                node={nodes[2]}
                preloadOptions={{ media: '(min-width: 601px)' }}
                relativePath={true}
                size="compact"
              />
            )}
          </div>
          <div className="top-story secondary-story mobile">
            {nodes[1] && (
              <FeaturedCard
                fetchPriority="high"
                imageWidth={150}
                loading="eager"
                node={nodes[1]}
                preloadOptions={{ media: '(max-width: 600px)' }}
                relativePath={true}
                size="compact"
              />
            )}
            {nodes[2] && (
              <FeaturedCard
                imageWidth={150}
                node={nodes[2]}
                preloadOptions={{ media: '(max-width: 600px)' }}
                relativePath={true}
                size="compact"
              />
            )}
            {nodes[3] && (
              <FeaturedCard
                imageWidth={150}
                node={nodes[3]}
                preloadOptions={{ media: '(max-width: 600px)' }}
                relativePath={true}
                size="compact"
              />
            )}
            {nodes[4] && (
              <FeaturedCard
                imageWidth={150}
                node={nodes[4]}
                preloadOptions={{ media: '(max-width: 600px)' }}
                relativePath={true}
                size="compact"
              />
            )}
          </div>
        </div>
      ) : null}
    </FeaturedSectionWrapper>
  );
};

const FeaturedSectionWrapper = styled.div`
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .top-story.desktop,
  .top-story.primary-story {
    .post-card-article-link .post-title {
      color: #ffffff;
    }
  }

  .secondary-stories {
    grid-column: span 2 / span 2;
    @media (min-width: 1024px) {
      grid-column: span 1 / span 2;
    }
  }

  .primary-story {
    grid-column: span 2 / span 2;
    .post-card {
      height: 416px;
      @media screen and (max-width: 800px) {
        height: auto;
        min-height: 300px;
      }
    }
    .post-card-text-wrapper {
      min-height: 5em;
    }
    @media (max-width: 768px) {
      .post-card-image {
        height: 220px;
      }
    }
  }
  .secondary-story {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    margin: 0 !important;
    margin-left: auto;
    margin-right: auto;
    .post-card {
      height: 200px;
    }
    &.mobile {
      display: none;
    }
    @media (min-width: 1024px) {
      gap: 16px;
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
    @media screen and (max-width: 800px) {
      margin-top: 12px;
      height: auto;
      border: none;
      &.desktop {
        display: none !important;
      }
      &.mobile {
        display: flex !important;
        flex-direction: column;

        .post-card {
          display: flex;
          flex-direction: row;
          height: unset;

          .post-title {
            font-size: 1rem;
            line-height: 1.5rem;
          }
        }

        .image-wrapper {
          height: 100px;
          width: 140px;
          min-width: 140px;
          max-width: 140px;
          margin-right: 1rem;
          margin-bottom: 0;
        }
      }
    }
  }
`;
