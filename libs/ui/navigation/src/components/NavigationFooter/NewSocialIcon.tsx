import React from 'react';
import styled from '@benzinga/themetron';
import { SocialSite } from './NewFooterEntity';

const StyledAnchor = styled.a<{ color?: string; className?: string }>`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  border-radius: 0.25rem;
  font-size: ${({ theme }) => theme.fontSize['3xl']};
  color: ${props => props.color};
  -webkit-transition:
    color 200ms cubic-bezier(0.25, 0.46, 0.45, 0.94),
    color 200ms ease;
  transition: opacity 0.3s ease;
  &:hover {
    opacity: 0.8;
  }
`;

interface SocialIconProps extends SocialSite {
  isAmp?: boolean;
}

const NewSocialIcon: React.FC<SocialIconProps> = ({ icon, link, name }) => {
  return (
    <StyledAnchor
      aria-label={`Click to check out Benz<PERSON>'s ${name}`}
      className={name}
      href={link}
      rel="noopener noreferrer"
      target="_blank"
    >
      <img alt={name} decoding="async" fetchPriority="low" height={24} loading="lazy" src={icon} width={24} />
    </StyledAnchor>
  );
};
export default NewSocialIcon;
