import { QuoteProtos, ScannerProtos } from '@benzinga/scanner-manager';

export const isRankingScreener = (tableId: string) => {
  return Object.keys(rankingConfig).includes(tableId);
};

export const getRankingTableConfig = (tableId: string) => {
  return rankingConfig[tableId] ?? { layout: 'rankings', scanner_query: {}, table_columns: [] };
};

interface RankingTickers extends QuoteProtos.IQuote {
  rankingValue?: number;
}

export interface RankingTable {
  rankings: RankingTickers[];
  columns: RankingColumn[];
  query: ScannerProtos.IQuery;
  slug?: string;
}
export interface RankingColumn {
  field: string;
  format: string;
  gated: boolean;
  label: string;
  bold?: boolean;
}

export type RankingConfig = {
  layout: string;
  scanner_query: {
    fields: string[];
    filtersAsString: string;
    limit: number;
    offset: number;
    sortDir: number;
    sortField: string;
  };
  table_columns: RankingColumn[];
};

const rankingConfig = {
  'top-growth-stocks': {
    layout: 'rankings',
    scanner_query: {
      fields: [
        'symbol',
        'growthPercentile',
        'marketCap',
        'roe',
        'roa',
        'high52Week',
        'low52Week',
        'name',
        'gicsSectorName',
        'price',
      ],
      filtersAsString: 'subtype_in_ADR,COMMON_SHARE,ETF;growthPercentile_bt_,;marketCap_bt_25000000,',
      limit: 100,
      offset: 0,
      sortDir: 1,
      sortField: 'growthPercentile',
    },
    table_columns: [
      { field: 'symbol', format: 'string', gated: true, label: 'Name/Industry' },
      { bold: true, field: 'price', format: 'price', gated: true, label: 'Price' },
      { bold: true, field: 'growthPercentile', format: 'string', gated: false, label: 'Growth Rank' },
      { field: 'marketCap', format: 'longPrice', gated: false, label: 'Market Cap' },
      { field: 'roe', format: 'percent', gated: false, label: 'ROE (ttm)' },
      { field: 'roa', format: 'percent', gated: false, label: 'ROA (ttm)' },
      { field: 'high52Week', format: 'price', gated: false, label: '52W High' },
      { field: 'low52Week', format: 'price', gated: false, label: '52W Low' },
    ],
  },
  'top-momentum-stocks': {
    layout: 'rankings',
    scanner_query: {
      fields: [
        'symbol',
        'momentumPercentile',
        'marketCap',
        'rsi',
        'ytdChange',
        'high52Week',
        'low52Week',
        'name',
        'gicsSectorName',
        'price',
      ],
      filtersAsString: 'subtype_in_ADR,COMMON_SHARE,ETF;momentumPercentile_bt_,;marketCap_bt_25000000,',
      limit: 100,
      offset: 0,
      sortDir: 1,
      sortField: 'momentumPercentile',
    },
    table_columns: [
      { field: 'symbol', format: 'string', gated: true, label: 'Name/Industry' },
      { bold: true, field: 'price', format: 'price', gated: true, label: 'Price' },
      { bold: true, field: 'momentumPercentile', format: 'string', gated: false, label: 'Momentum Rank' },
      { field: 'marketCap', format: 'longPrice', gated: false, label: 'Market Cap' },
      { field: 'rsi', format: 'number', gated: false, label: 'RSI' },
      { field: 'ytdChange', format: 'percent', gated: false, label: 'YTD Perf.' },
      { field: 'high52Week', format: 'price', gated: false, label: '52W High' },
      { field: 'low52Week', format: 'price', gated: false, label: '52W Low' },
    ],
  },
  'top-quality-stocks': {
    layout: 'rankings',
    scanner_query: {
      fields: [
        'symbol',
        'qualityPercentile',
        'marketCap',
        'roic',
        'grossMargin',
        'high52Week',
        'low52Week',
        'name',
        'gicsSectorName',
        'price',
      ],
      filtersAsString: 'subtype_in_ADR,COMMON_SHARE,ETF;qualityPercentile_bt_,;marketCap_bt_25000000,',
      limit: 100,
      offset: 0,
      sortDir: 1,
      sortField: 'qualityPercentile',
    },
    table_columns: [
      { field: 'symbol', format: 'string', gated: true, label: 'Name/Industry' },
      { bold: true, field: 'price', format: 'price', gated: true, label: 'Price' },
      { bold: true, field: 'qualityPercentile', format: 'string', gated: false, label: 'Quality Rank' },
      { field: 'marketCap', format: 'longPrice', gated: false, label: 'Market Cap' },
      { field: 'roic', format: 'percent', gated: false, label: 'ROIC (ttm)' },
      { field: 'grossMargin', format: 'percent', gated: false, label: 'Gross Margin (ttm)' },
      { field: 'high52Week', format: 'price', gated: false, label: '52W High' },
      { field: 'low52Week', format: 'price', gated: false, label: '52W Low' },
    ],
  },
  'top-value-stocks': {
    layout: 'rankings',
    scanner_query: {
      fields: [
        'symbol',
        'valuePercentile',
        'marketCap',
        'priceToEarnings',
        'priceToSales',
        'high52Week',
        'low52Week',
        'name',
        'gicsSectorName',
        'price',
      ],
      filtersAsString: 'subtype_in_ADR,COMMON_SHARE,ETF;valuePercentile_bt_,;marketCap_bt_25000000,',
      limit: 100,
      offset: 0,
      sortDir: 1,
      sortField: 'valuePercentile',
    },
    table_columns: [
      { field: 'symbol', format: 'string', gated: true, label: 'Name/Industry' },
      { bold: true, field: 'price', format: 'price', gated: true, label: 'Price' },
      { bold: true, field: 'valuePercentile', format: 'string', gated: false, label: 'Value Rank' },
      { field: 'marketCap', format: 'longPrice', gated: false, label: 'Market Cap' },
      { field: 'priceToEarnings', format: 'number', gated: false, label: 'P/E (ttm)' },
      { field: 'priceToSales', format: 'number', gated: false, label: 'P/S (ttm)' },
      { field: 'high52Week', format: 'price', gated: false, label: '52W High' },
      { field: 'low52Week', format: 'price', gated: false, label: '52W Low' },
    ],
  },
};
