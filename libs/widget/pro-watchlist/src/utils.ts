import { DefaultTableParameters } from '@benzinga/ag-grid-utils';

export const DEFAULT_WATCHLIST_CONFIG = {
  flightMode: false,
  sendGroup: null,
  table: {
    ...DefaultTableParameters,
    columns: [
      {
        colId: 'symbol',
        hide: false,
        pinned: 'left' as const,
        width: 100,
      },
      {
        colId: 'name',
        hide: false,
      },
      {
        colId: 'price',
        hide: false,
      },
      {
        colId: 'quantity',
        hide: false,
      },
      {
        colId: 'sparkline0',
        hide: false,
      },
      {
        colId: 'open',
        hide: false,
      },
      {
        colId: 'regularHoursChange',
        hide: false,
      },
      {
        colId: 'regularHoursPercentChange',
        hide: false,
      },
      {
        colId: 'change',
        hide: false,
      },
      {
        colId: 'percentChange',
        hide: false,
      },
      {
        colId: 'postToPreChange',
        hide: false,
      },
      {
        colId: 'postToPrePercentChange',
        hide: false,
      },
      {
        colId: 'high',
        hide: false,
      },
      {
        colId: 'low',
        hide: false,
      },
      {
        colId: 'dayVolume',
        hide: false,
      },
      {
        colId: 'marketCap',
        hide: false,
      },
      {
        colId: 'PriceAlerts',
        hide: false,
      },
      {
        colId: 'notes',
        hide: false,
      },
    ],
  },
  watchlistId: null,
};
