'use client';
import { Log, Logging<PERSON>anager, StockSymbol } from '@benzinga/session';
import { PermissionOverlay, SendLinkContextProvider } from '@benzinga/pro-ui';
import { TableParameters } from '@benzinga/ag-grid-utils';
import styled, { TC } from '@benzinga/themetron';
import { SessionContext } from '@benzinga/session-context';
import { Watchlist, WatchlistManager, WatchlistId, WatchlistManagerEvent } from '@benzinga/watchlist-manager';
import {
  useWidgetParameters,
  WidgetManifest,
  WidgetToolbarContext,
  useFlightModeToolBar,
  useGlobalSetting,
} from '@benzinga/widget-tools';
import { ProContext } from '@benzinga/pro-tools';
import { hasLength, noop } from '@benzinga/utils';
import Hooks from '@benzinga/hooks';

import React from 'react';

import { Spinner } from '@benzinga/core-ui';
import { WatchlistToolbar } from './toolbar';
import { WatchlistGrid } from './grid';
import { Tooltip } from 'antd';
import { WidgetLinkingID } from '@benzinga/widget-linking';
import { WatchlistWidgetIteration, WatchlistWidgetAllIterations } from './entities';
import { WatchlistWidgetMigrator } from './migrator';
import { TrackingManager } from '@benzinga/tracking-manager';
import { Chart } from '@benzinga/valentyn-icons';
import { WatchlistSettings } from './globalSettings';
import { useNavigate } from 'react-router-dom';
import { Help, Settings, Watchlist as WatchlistIcon } from '@benzinga/themed-icons';
import { DEFAULT_WATCHLIST_CONFIG } from './utils';

interface State {
  watchlists: Watchlist[];
}

export const WatchlistWidget: React.FC = () => {
  const widgetToolbar = React.useContext(WidgetToolbarContext);
  const proContext = React.useContext(ProContext);
  const session = React.useContext(SessionContext);
  const widgetParams = useWidgetParameters(WatchlistWidgetManifest);
  const globalParams = useGlobalSetting(WatchlistWidgetManifest);

  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  const gridSubscriberRef = React.useRef<React.ElementRef<typeof WatchlistGrid> | null>(null);
  const [isLoaded, setIsLoaded] = React.useState<boolean | undefined>(() => undefined);
  const [state, setState] = React.useState<State>(() => ({
    watchlists: session.getManager(WatchlistManager).getStoredWatchlists(),
  }));
  const onSelectWatchlist = React.useCallback(
    (watchlistId: WatchlistId) => {
      session.getManager(TrackingManager).trackWatchlistEvent('view', {
        watchlist_id: watchlistId,
      });
      const setParameters = widgetParams.setParameters;
      setParameters(old => ({ ...old, watchlistId }));
    },
    [widgetParams.setParameters, session],
  );

  const watchlist = React.useMemo<Watchlist | undefined>(() => {
    if (hasLength(widgetParams.parameters.watchlistId)) {
      return state.watchlists.find(w => w.watchlistId === widgetParams.parameters.watchlistId);
    } else {
      return undefined;
    }
  }, [widgetParams.parameters.watchlistId, state]);

  if (isLoaded === undefined) {
    setIsLoaded(!!watchlist);
  }

  React.useEffect(() => {
    const defineControls = widgetToolbar.defineControls;
    const openHelpPage = proContext.openHelpPage;

    if (openHelpPage) {
      defineControls({
        key: 'quick_tip',
        submenuNode: {
          action: () => openHelpPage('widgets/watchlist'),
          icon: <Help />,
          key: 'quick_tip',
          name: 'Watchlist Tips',
          type: 'Item',
        },
      });
    }
  }, [proContext.openHelpPage, widgetToolbar.defineControls, widgetToolbar.setIcon, widgetToolbar.setTitle]);

  const navigate = useNavigate();
  React.useEffect(() => {
    const defineControls = widgetToolbar.defineControls;
    const dropdownItems =
      state.watchlists
        .map(wl => ({
          id: wl.watchlistId,

          onClick: () => {
            onSelectWatchlist(wl.watchlistId);
          },
          value: {
            render: (
              <div style={{ display: 'flex', flexWrap: 'nowrap' }}>
                <span style={{ flexGrow: 0, flexShrink: 1, overflow: 'hidden' }}>{wl.name ?? ''} </span>
              </div>
            ),
            sortName: wl.name,
          },
        }))
        .sort(TC.sortDropDownMenuItems) ?? [];

    const button = widgetParams.parameters.watchlistId ? (
      <Tooltip mouseEnterDelay={0.5} placement={'bottom'} title={watchlist?.name ?? ''}>
        <div style={{ display: 'flex', flex: '1 1 0', flexWrap: 'nowrap', overflow: 'hidden' }}>
          <span
            style={{ flexGrow: 0, flexShrink: 1, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
          >
            {watchlist?.name ?? ''}{' '}
          </span>
        </div>
      </Tooltip>
    ) : (
      <></>
    );

    defineControls(
      {
        key: 'watchlistDropdown',
        toolbarNode: (
          <WatchlistDropDown
            button={{ type: 'toolbar', value: button }}
            className="TUTORIAL_Watchlist_Watchlists"
            dropdown={{ position: 'right' }}
            items={dropdownItems || []}
            sort={false}
          />
        ),
      },
      {
        key: 'settings',
        submenuNode: {
          action: () => {
            navigate('preferences/settings/watchlist/');
          },
          icon: <Settings key="settings" />,
          key: 'settings',
          name: 'Watchlist Settings',
          type: 'Item',
        },
      },
    );
  }, [
    onSelectWatchlist,
    watchlist?.name,
    state,
    widgetToolbar.defineControls,
    widgetParams.parameters.watchlistId,
    navigate,
  ]);

  const setFlightMode = React.useCallback(
    (flightMode: boolean) => {
      const setParameters = widgetParams.setParameters;
      setParameters(old => ({ ...old, flightMode }));
    },
    [widgetParams.setParameters],
  );
  useFlightModeToolBar(widgetParams.parameters.flightMode, setFlightMode);

  React.useEffect(() => {
    const setControlsDisplay = widgetToolbar.setControlsDisplay;
    const controlsDisplay = [
      'watchlistDropdown',
      'settings',
      'quick_tip',
      'popoutTool',
      'linking',
      'flight_mode',
      'submenu',
      'closeTool',
    ];

    setControlsDisplay(old =>
      [...old, ...controlsDisplay].filter(
        v =>
          ![
            ...(proContext.isPopout ? ['popoutTool', 'settings'] : []),
            ...(widgetParams.parameters.flightMode ? [] : ['watchlistDropdown']),
          ].includes(v),
      ),
    );
  }, [widgetParams.parameters.flightMode, widgetToolbar.setControlsDisplay, proContext.isPopout]);

  React.useEffect(() => {
    const setToolbar = widgetToolbar.setToolbar;
    if (!widgetParams.parameters.flightMode) {
      setToolbar(watchlist ? watchlist.name : 'New Watchlist');
    } else {
      setToolbar(undefined);
    }
  }, [widgetParams.parameters.flightMode, watchlist, widgetToolbar.setToolbar]);

  const selectDefaultWatchlist = React.useCallback(
    async (force?: boolean, watchlistId?: string | null) => {
      let watchlistsToPick: Watchlist[] = [];
      const watchlistManager = session.getManager(WatchlistManager);
      if (!force) {
        watchlistsToPick = watchlistManager.getStoredWatchlists();
      } else {
        const response = await watchlistManager.getWatchlists(true);
        if (response.ok) {
          watchlistsToPick = response.ok;
          setIsLoaded(true);
        }
      }
      if (
        watchlistsToPick.length !== 0 &&
        (!watchlistId || watchlistManager.getStoredWatchlistById(watchlistId) === undefined)
      ) {
        const firstWatchlistId = watchlistsToPick[0];
        if (firstWatchlistId.watchlistId) onSelectWatchlist(firstWatchlistId.watchlistId);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [onSelectWatchlist, session],
  );

  const [throttledErrorMessage] = Hooks.useThrottle(
    (log: Log) => {
      session.getManager(LoggingManager).log('error', log, ['toast']);
    },
    5 * 60 * 1000,
  );

  Hooks.useSubscriber(session.getManager(WatchlistManager), (event: WatchlistManagerEvent) => {
    let msg;
    switch (event.type) {
      case 'watchlist:updated_watchlists':
        setState(s => ({
          ...s,
          watchlists: event.watchlists,
        }));
        break;
      case 'watchlist:removing_watchlist':
        if (widgetParams.parameters.watchlistId === event.watchlist.watchlistId) {
          selectDefaultWatchlist(false);
        }
        break;
      case 'watchlist:added_ticker_to_watchlist':
        session.getManager(LoggingManager).log(
          'log',
          {
            category: event.type,
            message: `${event.symbols.reduce((p, c) => p + ', ' + c)} symbol(s) were added to ${event.watchlist.name} Watchlist successfully`,
          },
          ['toast'],
        );
        break;
      case 'watchlist:removed_ticker_from_watchlist':
        session.getManager(LoggingManager).log(
          'log',
          {
            category: event.type,
            message: `${event.symbols.reduce((p, c) => p + ', ' + c)} symbol(s) were removed from ${event.watchlist.name} Watchlist successfully`,
          },
          ['toast'],
        );
        break;
      case 'error': {
        switch (event.errorType) {
          case 'watchlist:get_watchlist_error':
            msg =
              'Unable to fetch your watchlists. Please refresh Pro, or logout and try again after logging in. If you continue to experience further issues, please contact our support team.';
            break;
          case 'watchlist:add_ticker_to_watchlist_error':
          case 'watchlist:remove_watchlist_ticker_error':
            msg = `${event.symbols.reduce((p, c) => {
              const result = p.length > 0 ? p + ', ' + c : c;
              if (result.length <= 30) {
                return result;
              }
              return result.substring(0, 27) + '...';
            }, '')} was not updated in the ${event.watchlist.name} Watchlist. Please refresh Pro and try again or contact our support team.`;
            session.getManager(LoggingManager).log(
              'error',
              {
                category: event.type,
                message: msg,
              },
              ['toast'],
            );
            return;
          default:
            msg =
              'Facing errors with watchlists. Please refresh Pro, or logout and try again after logging in. If you continue to experience further issues, please contact our support team.';
            break;
        }
        const error = event.error;
        if (error?.type === 'auth_required' || (error?.data as Response)?.status === 424) return;
        throttledErrorMessage({
          category: event.type,
          message: msg,
        });
        break;
      }
    }
  });

  // Only used to initialize watchlist on load
  React.useEffect(() => {
    selectDefaultWatchlist(true, widgetParams.parameters.watchlistId);
  });

  React.useEffect(() => {
    if (proContext.isPopout) {
      const watchlistName = watchlist ? `${watchlist.name} - ` : '';
      document.title = `${watchlistName}Benzinga Pro Watchlist`;
    }
  }, [proContext.isPopout, watchlist]);

  const removeSymbolsFromList = React.useCallback(
    (symbols: StockSymbol[]) => {
      if (widgetParams.parameters.watchlistId === null) {
        return;
      }

      if (watchlist === undefined) {
        return;
      }

      const foundSymbols = symbols.flatMap(symbol => watchlist.symbols.find(s => s.symbol === symbol) ?? []);
      if (foundSymbols.length > 0) {
        const watchlistManager = session.getManager(WatchlistManager);
        watchlistManager.removeTickersFromWatchlist(watchlist, foundSymbols);
      }
    },
    [session, watchlist, widgetParams.parameters.watchlistId],
  );

  const watchlists = session.getManager(WatchlistManager).getStoredWatchlists();

  const onGridLayoutChanged = React.useCallback(
    (table: Partial<TableParameters>) => {
      const setParameters = widgetParams.setParameters;
      setParameters(old => ({ ...old, table: { ...old.table, ...table } }));
    },
    [widgetParams.setParameters],
  );

  const renderWatchlist = React.useMemo(() => {
    if (watchlists.length === 0 || !watchlist) {
      return (
        <div className="NoResults NoResults-simple">
          <div className="NoResults-headerIcon">
            <WatchlistIcon />
          </div>
          <div className="title">You don&apos;t have any watchlists.</div>
        </div>
      );
    }
    return (
      <WatchlistGrid
        autoselectTicker={globalParams.settings.enableTickerAutoselect}
        gridLayout={widgetParams.parameters.table}
        onGridLayoutChanged={onGridLayoutChanged}
        ref={gridSubscriberRef}
        removeSymbolsFromList={removeSymbolsFromList}
        watchlist={watchlist}
      />
    );
  }, [
    globalParams.settings.enableTickerAutoselect,
    onGridLayoutChanged,
    removeSymbolsFromList,
    watchlist,
    watchlists.length,
    widgetParams.parameters.table,
  ]);

  const setGroup = React.useCallback(
    (sendGroup: WidgetLinkingID) => {
      const setParameters = widgetParams.setParameters;
      setParameters(old => ({ ...old, sendGroup: sendGroup }));
    },
    [widgetParams.setParameters],
  );

  return (
    <SendLinkContextProvider displayModal={false} linkId={widgetParams.parameters.sendGroup} setLink={setGroup}>
      <PermissionOverlay
        permission={{ action: 'bzpro/widget/use', resource: 'watchlist' }}
        text="Login to start using watchlists"
      >
        <Holder className="Widget--watchlist">
          {isLoaded ? (
            <>
              {!widgetParams.parameters.flightMode && (
                <WatchlistToolbar
                  gridSubscriberRef={gridSubscriberRef}
                  onSelectWatchlist={onSelectWatchlist}
                  removeSymbolsFromList={removeSymbolsFromList}
                  selectedWatchlistId={widgetParams.parameters.watchlistId ?? undefined}
                />
              )}
              {renderWatchlist}
            </>
          ) : (
            <Spinner />
          )}
        </Holder>
      </PermissionOverlay>
    </SendLinkContextProvider>
  );
};

const Holder = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const WatchlistDropDown = styled(TC.DropdownMenu)`
  border-right: 1px solid ${props => props.theme.colors.border};
  min-width: 50px;
  flex-grow: 1;
  margin-right: 5px;
  ${TC.ToolbarButton} {
    padding: 0px 3px 0px 5px;
  }
`;

export const WatchlistWidgetManifest: WidgetManifest<
  'watchlist',
  WatchlistWidgetIteration,
  WatchlistWidgetAllIterations
> = {
  SettingsRender: WatchlistSettings,
  WidgetRender: WatchlistWidget,
  daemon: noop,
  defaultGlobalParameters: {
    enableTickerAutoselect: false,
  },
  defaultWidgetParameters: DEFAULT_WATCHLIST_CONFIG,
  description: 'Watchlist',
  icon: Chart,
  id: 'watchlist',
  menuItem: true,
  migrator: WatchlistWidgetMigrator,
  name: 'Watchlist',
  permission: { action: 'bzpro/widget/use', resource: 'watchlist' },
  state: 'production',
  version: 4,
};
