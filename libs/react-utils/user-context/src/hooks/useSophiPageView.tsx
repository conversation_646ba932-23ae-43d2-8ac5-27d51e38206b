import { useEffect, useContext, useRef } from 'react';
import { sophiManager } from '@benzinga/ads-utils';
import { SessionContext } from '@benzinga/session-context';

interface SophiTrackingData {
  [key: string]: any;
  page_type: string;
  page_section: string;
}

export interface UseSophiPageViewOptions {
  pageType: string;
  section?: string;
  trackingData?: Partial<SophiTrackingData>;
  isArticlePage?: boolean;
  disabled?: boolean;
}

export const useSophiPageView = ({
  disabled = false,
  isArticlePage = false,
  pageType,
  section = '',
  trackingData = {},
}: UseSophiPageViewOptions): null => {
  const session = useContext(SessionContext);
  const hasTrackedRef = useRef<boolean>(false);

  useEffect(() => {
    if (disabled) return;
    if (hasTrackedRef.current) return;

    console.log(`[Sophi] Page view tracking - Type: ${pageType}, Section: ${section}`);

    sophiManager.trackPageView(isArticlePage, section);

    const baseTrackingData: SophiTrackingData = {
      page_section: section,
      page_type: pageType,
      ...trackingData,
    };

    console.log('[Sophi] Tracking page view with data:', baseTrackingData);
    sophiManager.trackWithData(session, 'page_view', baseTrackingData);
    hasTrackedRef.current = true;
  }, [session, pageType, section, trackingData, isArticlePage, disabled]);

  return null;
};
