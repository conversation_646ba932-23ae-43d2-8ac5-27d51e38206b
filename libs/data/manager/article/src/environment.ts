export class ArticleEnvironment {
  public static getName = () => 'benzinga-article';
  public static getEnvironment = (env: Record<string, string>) => ({
    analyticsUrl: new URL(env.analyticsUrl ?? 'https://www.benzinga.com'),
    campaignsUrl: new URL(env.campaignsUrl ?? 'https://www.benzinga.com'),
    commentsUrl: new URL(env.commentsUrl ?? 'https://www.benzinga.com/'),
    editorialPreviewKey: env.editorialPreviewKey ?? 'mz6rEWsR5S',
    editorialToolsUrl: new URL(env.editorialToolsUrl ?? 'https://editorial-tools.benzinga.com/'),
    externalUrl: new URL(env.externalUrl ?? 'https://www.benzinga.com/'),
    internalContentKey: env.internalContentKey ?? '1FAYydyNGBMSxEdGqvJ3LkRJuWeF495x',
    internalContentUrl: new URL(env.internalContentUrl ?? 'https://content-internal.benzinga.com/'),
    key: env.key ?? '2RiuR92vjytxS8r93w3c8WTpGSd3y9Gk',
    relevantUrl: new URL(env.relevantUrl ?? 'https://www.benzinga.com/'),
    url: new URL(env.url ?? 'https://www.benzinga.com/'),
    wnstnUrl: new URL(env.wnstnUrl ?? 'https://editorial-tools.benzinga.com/api/v2/wnstn/'),
  });
}
