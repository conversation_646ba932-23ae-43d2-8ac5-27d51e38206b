import {
  AdvancedNewsExpression,
  AdvancedNewsfeedWidget,
  MarketMoversNews,
  getBlankFilters,
  lowImportancePreset,
  offImportancePreset,
} from '@benzinga/pro-newsfeed-widget';
import { DEFAULT_WATCHLIST_CONFIG, WatchlistWidget } from '@benzinga/pro-watchlist-widget';
import { Scanner, ScannerWidgetManifest } from '@benzinga/pro-scanner-widget';
import { ScannerProtos } from '@benzinga/scanner-manager';
import { ScannerConfig } from '@benzinga/scanner-config-manager';
import { TC } from '@benzinga/themetron';
import { arrayShallowEqual, noop, objectShallowEqual } from '@benzinga/utils';
import { WatchlistId } from '@benzinga/watchlist-manager';
import {
  WidgetContextProvider,
  WidgetParametersContextProvider,
  WidgetToolbarContextProvider,
} from '@benzinga/widget-tools';
import { Alert } from 'antd';
import { RootState } from '../../../redux/types';
import classnames from 'classnames';
import React from 'react';
import { Mosaic, MosaicBranch, MosaicNode, MosaicWindow } from 'react-mosaic-component';
import { connect } from 'react-redux';

import styled from '@benzinga/themetron';
import { WidgetWindowBar } from '../../widgets/WidgetWindowBar';
import { Workspace } from '../workspaceEntity';
import { selectActiveWorkspaceId, selectWorkspaceById } from '../workspaceSelectors';
import { HomepageNewWidget } from '../../ui/homepageNewWidget';
import { ProContext, WorkspaceContextProvider } from '@benzinga/pro-tools';
import { WidgetConfig } from '@benzinga/widget-tools';
import { generate } from 'shortid';
import { PermissionOverlay } from '@benzinga/pro-ui';
import { Permission, StockSymbol } from '@benzinga/session';
import { useIsUserLoggedIn } from '@benzinga/user-context';
import { SessionContext } from '@benzinga/session-context';
import { PermissionsManager } from '@benzinga/permission-manager';
import { useMarketMovingNews } from '@benzinga/news-manager-hooks';
import { MarketingWins } from '../MarketingWins';
import { WidgetLinkingManager } from '@benzinga/widget-linking';
import { Manifest } from '../../../manifest';
import { DefaultTableParameters } from '@benzinga/ag-grid-utils';

interface ReduxState {
  isActiveWorkspace: boolean;
  workspace: Workspace;
}

type Props = ReduxState;

const scannerConfig: ScannerConfig = {
  filters: [{ field: 'subtype', operator: 'in', parameters: ['ADR', 'COMMON_SHARE', 'ETF'] }],
  limit: 25,
  refreshInterval: 60,
  sortDir: ScannerProtos.SortDir.DESC,
  sortField: 'changePercent',
  source: 'stocks',
  tableParameters: {
    ...DefaultTableParameters,
    columns: [
      { colId: 'symbol', hide: false },
      { colId: 'changePercent', hide: false },
      { colId: 'price', hide: false },
      { colId: 'change', hide: false },
      { colId: 'marketCap', hide: false },
      { colId: 'dayVolume', hide: false },
    ],
  },
  tags: [],
};

const trendingConfig: ScannerConfig = {
  filters: [{ field: 'symbol', operator: 'in', parameters: ['QQQ', 'SPY', 'AAPL', 'F', 'XOM'] }],
  limit: 25,
  refreshInterval: 60,
  sortDir: ScannerProtos.SortDir.DESC,
  sortField: 'changePercent',
  source: 'stocks',
  tableParameters: {
    ...DefaultTableParameters,
    columns: [
      { colId: 'symbol', hide: false },
      { colId: 'changePercent', hide: false },
      { colId: 'price', hide: false },
      { colId: 'change', hide: false },
      { colId: 'marketCap', hide: false },
      { colId: 'dayVolume', hide: false },
    ],
  },
  tags: [],
};

const TopNews = () => {
  const isLoggedIn = useIsUserLoggedIn();
  const feedExpression = React.useMemo(
    () =>
      AdvancedNewsExpression.fromNewsExpressionType({
        lhs: { expression: ['Tickers.name', 'any', ['SPY']], operator: 'expression' },
        operator: 'or',
        rhs: {
          // Top Stories and Market moving exclusive
          expression: ['Channels.tid', 'any', isLoggedIn ? [138079] : [138079, 165347]],
          operator: 'expression',
        },
      }).getExpressionType(),
    [isLoggedIn],
  );

  const session = React.useContext(SessionContext);
  const permissionManager = session?.getManager(PermissionsManager);
  const hasAccessToAll = permissionManager?.hasAccess('bzpro/newsfeed/filter', '#')?.ok ?? false;
  const hasAccessToImportance =
    (hasAccessToAll || permissionManager?.hasAccess('bzpro/newsfeed/filter/importance', '#')?.ok) ?? false;
  return (
    <WidgetParametersContextProvider
      parameters={React.useMemo(
        () => ({
          config: {
            feedExpression: feedExpression,
            feedSettings: getBlankFilters({
              importance: hasAccessToImportance ? lowImportancePreset : offImportancePreset,
              sources: ['story'],
            }),
          },
          displayType: 'Title',
          flightMode: true,
          setFlightMode: noop,
        }),
        [feedExpression, hasAccessToImportance],
      )}
      setParameters={noop}
    >
      <AdvancedNewsfeedWidget />
    </WidgetParametersContextProvider>
  );
};

const Watchlist = () => {
  const [watchlistId, setWatchlistId] = React.useState<WatchlistId | undefined>(undefined);

  // const setSelectedWatchlistCallback = React.useCallback((id: string | undefined))

  return (
    <WidgetParametersContextProvider
      parameters={React.useMemo(
        () => ({
          ...DEFAULT_WATCHLIST_CONFIG,
          watchlistId,
        }),
        [watchlistId],
      )}
      setParameters={React.useCallback(callback => {
        setWatchlistId(oldWatchlistId => {
          const parameters = callback({
            ...DEFAULT_WATCHLIST_CONFIG,
            watchlistId: oldWatchlistId,
          });

          return parameters.watchlistId;
        });
      }, [])}
    >
      <WatchlistWidget />
    </WidgetParametersContextProvider>
  );
};

interface WidgetDefinition {
  permission?: boolean | Permission;
  render: React.FC;
  widgetConfig?: WidgetConfig;
  widgetType?: string;
}

const widgets: Record<string, WidgetDefinition> = {
  'Biggest Gainers': {
    render: (() => (
      <WidgetParametersContextProvider
        parameters={React.useMemo<(typeof ScannerWidgetManifest)['defaultWidgetParameters']>(
          () => ({
            ...ScannerWidgetManifest.defaultWidgetParameters,
            flightMode: true,
            hideFooter: true,
            hideToolbar: true,
            widgetScan: {
              ...scannerConfig,
              sortDir: ScannerProtos.SortDir.DESC,
            },
          }),
          [],
        )}
        setParameters={noop}
      >
        <Scanner />
      </WidgetParametersContextProvider>
    )) as React.FC,
    widgetConfig: {
      parameters: {
        widgetScan: {
          ...scannerConfig,
          sortDir: ScannerProtos.SortDir.DESC,
        },
      },
      widgetVersion: 9,
    },
    widgetType: 'scanner',
  },
  'Biggest Losers': {
    render: (() => (
      <WidgetParametersContextProvider
        parameters={React.useMemo<(typeof ScannerWidgetManifest)['defaultWidgetParameters']>(
          () => ({
            ...ScannerWidgetManifest.defaultWidgetParameters,
            flightMode: true,
            hideFooter: true,
            hideToolbar: true,
            widgetScan: {
              ...scannerConfig,
              sortDir: ScannerProtos.SortDir.ASC,
            },
          }),
          [],
        )}
        setParameters={noop}
      >
        <Scanner />
      </WidgetParametersContextProvider>
    )) as React.FC,
    widgetConfig: {
      parameters: {
        widgetScan: {
          ...scannerConfig,
          sortDir: ScannerProtos.SortDir.ASC,
        },
      },
      widgetVersion: 9,
    },
    widgetType: 'scanner',
  },
  "Today's Market Moving News: Stocks you can trade today": {
    permission: { action: 'bzpro/home', resource: 'market-moving-news' },
    render: (() => {
      const info = useMarketMovingNews();
      const proContext = React.useContext(ProContext);

      const onSymbolClick = React.useCallback(
        (symbol: string) => {
          proContext.addWidgetWithSymbol(symbol, 'details');
        },
        [proContext],
      );

      return (
        <PermissionOverlay
          permission={{ action: 'bzpro/home', resource: 'market-moving-news' }}
          text="Market Moving News requires an active subscription"
        >
          <MarketMoversNewsStyled info={info} onSymbolClick={onSymbolClick} />
        </PermissionOverlay>
      );
    }) as React.FC,
    widgetConfig: {
      parameters: {
        desktopNotificationsEnabled: false,
        feedExpression: {
          expression: ['Title', 'phrase', 'market-moving news for'],
          operator: 'expression',
        },
        feedSettings: getBlankFilters({
          importance: offImportancePreset,
          relevantCategories: { story: ['24'] },
          sources: ['story'],
        }),
        flightMode: false,
        sendGroup: null,
      },
      widgetVersion: 9,
    },
    widgetType: 'advancedNewsfeed',
  },
  'Top News': {
    render: (() => <TopNews />) as React.FC,
    widgetType: 'advancedNewsfeed',
  },

  Trending: {
    render: (() => (
      <WidgetParametersContextProvider
        parameters={React.useMemo(
          () => ({
            config: {
              ...trendingConfig,
              sortDir: ScannerProtos.SortDir.DESC,
            },
            flightMode: false,
            hideFooter: true,
            hideToolbar: true,
          }),
          [],
        )}
        setParameters={noop}
      >
        <Scanner />
      </WidgetParametersContextProvider>
    )) as React.FC,
    widgetType: 'scanner',
  },
  Wins: {
    permission: { action: 'bzpro/home', resource: 'market-moving-news' },
    render: (() => {
      const session = React.useContext(SessionContext);
      const onSymbolClick = React.useCallback(
        (symbol: StockSymbol) => {
          session.getManager(WidgetLinkingManager).getDefaultLinkFeed()?.pushEvent({
            symbol: symbol,
            type: 'ticker_selected',
          });
        },
        [session],
      );
      return (
        <PermissionOverlay
          permission={{ action: 'bzpro/home', resource: 'market-moving-news' }}
          text="This feature requires an active subscription"
        >
          <MarketingWinsStyled displayTopWin onSymbolClick={onSymbolClick} />
        </PermissionOverlay>
      );
    }) as React.FC,
  },
  'Your Watchlist': {
    render: (() => <Watchlist />) as React.FC,
    widgetType: 'watchlist',
  },
};

export const homePageWorkspace: Workspace = {
  config: {
    direction: 'row',
    first: {
      direction: 'column',
      first: {
        direction: 'row',
        first: 'Biggest Gainers',
        second: 'Biggest Losers',
        splitPercentage: 50,
      },
      second: 'Your Watchlist',
      splitPercentage: 50,
    },
    second: {
      direction: 'column',
      first: "Today's Market Moving News: Stocks you can trade today",
      second: 'Top News',
      splitPercentage: 50,
    },
    splitPercentage: 50,
  },
  lastAccessed: null,
  name: 'home-page',
  version: 3,
  workspaceId: 'home-page',
};

const HomeMenu: React.FC<Props> = props => {
  const config = homePageWorkspace.config as MosaicNode<keyof typeof widgets>;

  const renderWidget = (widgetId: keyof typeof widgets, path: MosaicBranch[]) => {
    const RenderChildren = widgets[widgetId].render;
    return (
      <WidgetHolderInner key={widgetId} path={path} title={widgetId}>
        <RenderChildren />
      </WidgetHolderInner>
    );
  };

  return (
    <WorkspaceContextProvider
      config={config}
      isActiveWorkspace={props.isActiveWorkspace}
      isWidgetPositionChanging={false}
    >
      <HomeMenuDiv selected={props.isActiveWorkspace}>
        <Mosaic<keyof typeof widgets>
          // className={classnames('mosaic-blueprint-theme', Classes.DARK)}
          initialValue={config}
          mosaicId={'main-window'}
          renderTile={renderWidget}
          resize={'DISABLED'}
        />
      </HomeMenuDiv>
    </WorkspaceContextProvider>
  );
};

interface Ownprops extends React.PropsWithChildren {
  path: MosaicBranch[];
  title: keyof typeof widgets;
}

const WidgetHolderInner: React.FC<React.PropsWithChildren<Ownprops>> = React.memo(
  props => {
    const [widgetRef, setWidgetRef] = React.useState<HTMLDivElement | null>(null);
    const widgetID = React.useRef(generate());
    const type = widgets[props.title].widgetType;

    return (
      <WidgetContextProvider widgetId={widgetID.current} widgetRef={widgetRef}>
        <WidgetToolbarContextProvider definition={{ title: props.title }}>
          <MosaicWindow<keyof typeof widgets>
            draggable={false}
            path={props.path ?? []}
            renderToolbar={() => (
              <div style={{ flex: '1' }}>
                <WidgetWindowBar boldText={true} />
              </div>
            )}
            title={props.title}
          >
            <WidgetWrapper className={classnames('Widget')} ref={setWidgetRef}>
              <Alert.ErrorBoundary>{props.children}</Alert.ErrorBoundary>
              {type && (
                <HomepageNewWidget
                  permission={widgets[props.title].permission}
                  widgetConfig={widgets[props.title].widgetConfig}
                  widgetType={type}
                >{`Open ${Manifest.find(m => m.id === type)?.name ?? 'None'}`}</HomepageNewWidget>
              )}
            </WidgetWrapper>
          </MosaicWindow>
        </WidgetToolbarContextProvider>
      </WidgetContextProvider>
    );
  },
  (prevProps, nextProps) => {
    const { path: prevPath, ...p } = prevProps;
    const { path: nextPath, ...n } = nextProps;

    if (objectShallowEqual(p as any, n as any) === false) {
      return false;
    } else if (prevPath === undefined || nextPath === undefined) {
      return prevPath === nextPath;
    } else {
      return arrayShallowEqual(prevPath, nextPath);
    }
  },
);

const MarketingWinsStyled = styled(MarketingWins)`
  height: 100%;
  margin: 0px;
`;

const MarketMoversNewsStyled = styled(MarketMoversNews)`
  height: 100%;
  margin: 0px;
`;

const WidgetWrapper = styled(TC.Column)`
  background: ${props => props.theme.colors.background};
  box-sizing: border-box;
  color: ${props => props.theme.colors.foreground};
  flex: 1;
  overflow: hidden;
  position: relative;
  &:hover div {
    opacity: 1;
  }
`;

const HomeMenuDiv = styled.div<{ selected: boolean }>`
  display: ${props => (props.selected ? 'block' : 'none')};
  height: 100%;
  margin: 0;
  width: 100%;

  .mosaic-tile {
    margin: 0px 0px 3px 3px;
  }

  .mosaic-root {
    position: initial;
  }

  .mosaic.mosaic-blueprint-theme .mosaic-window,
  .mosaic.mosaic-blueprint-theme .mosaic-preview {
    border-radius: initial !important;
    border-top-right-radius: initial !important;
    border-top-left-radius: initial !important;
    box-shadow: initial !important;
  }

  .mosaic-blueprint-theme {
    background: ${props => props.theme.colors.backgroundInactive} !important;
  }

  .mosaic.mosaic-blueprint-theme .mosaic-window {
    border-radius: initial !important;
    border-top-right-radius: initial !important;
    border-top-left-radius: initial !important;
    box-shadow: initial !important;
  }

  .mosaic-window-toolbar {
    align-items: end !important;
    background-color: ${props => props.theme.colors.background} !important;
    border-top-right-radius: 0px !important;
    border-top-left-radius: 0px !important;
    box-shadow: initial !important;
  }

  .draggable:hover {
    background: ${props => props.theme.colors.background} !important;
  }

  .mosaic-window-toolbar.draggable:hover {
    background: ${props => props.theme.colors.background} !important;
  }

  .mosaic-window-body {
    background: ${props => props.theme.colors.backgroundInactive} !important;
  }
`;

function mapStateToProps(state: RootState): ReduxState {
  return {
    isActiveWorkspace: selectActiveWorkspaceId(state) === 'home-page',
    workspace: selectWorkspaceById(state, { workspaceId: 'home-page' }),
  };
}

export default connect<ReduxState, Record<string, never>, Record<string, never>, RootState>(mapStateToProps)(HomeMenu);
