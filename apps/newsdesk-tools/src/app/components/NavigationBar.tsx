import { sortByString } from '@benzinga/utils';
import { Alignment, Icon, MenuDivider, MenuItem, Navbar, Position } from '@blueprintjs/core';
import { IconName } from '@blueprintjs/icons';
import { FunctionComponent } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router-dom';

import { RootState } from '../redux/types';

import { logout } from '../actions/session';
import {
  adminAccessSelector,
  hasNoCollectionAccessSelector,
  selectCollectionAccess,
} from '../selectors/accessSelectors';
import { PermissionGroup } from './collections/collectionEntities';
import {
  selectCollectionGroupIcon,
  selectCollectionGroupIds,
  selectCollectionGroupPluralName,
  selectCollectionIcon,
  selectCollectionIdsFromCollectionGroupId,
  selectCollectionName,
  selectCollectionPermissionResourceId,
  selectCollectionRoute,
} from './collections/collectionSelectors';
import DropdownMenu from './DropdownMenu';
import MenuLink from './MenuLink';

import Logo from './logos';
import Notifications from './notifications/Notifications';
import Postbox from './postbox';
import ScannerView from './scanner/ScannerPage';
import Squawk from './squawk/Squawk';
import { ChatComponent } from './chat/ChatComponent';

interface OwnProps {
  username: string;
}
interface DispatchableActions {
  logout: typeof logout;
}

interface ReduxState {
  currentState: RootState;
}

export type TopLevelPagesById = {
  [typeLevelPageId in TopLevelPageId]: TopLevelPage;
};

export enum TopLevelPageId {
  chat = 'chat',
  logo = 'logo',
  notifications = 'notifications',
  postbox = 'postbox',
  squawk = 'squawk',
  scanner = 'scanner',
}

export interface TopLevelPage {
  component: any;
  name: string;
  icon: IconName;
  route: string;
  topLevelPageId: TopLevelPageId;
}

type Props = OwnProps & DispatchableActions & ReduxState;

export const topLevelPagesById: TopLevelPagesById = {
  chat: {
    component: <ChatComponent />,
    icon: 'notifications',
    name: 'Chat',
    route: '/chat',
    topLevelPageId: TopLevelPageId.logo,
  },
  logo: {
    component: <Logo />,
    icon: 'media' as IconName,
    name: 'Logo',
    route: '/logo',
    topLevelPageId: TopLevelPageId.logo,
  },
  notifications: {
    component: <Notifications />,
    icon: 'notifications',
    name: 'Notifications',
    route: '/notifications',
    topLevelPageId: TopLevelPageId.notifications,
  },
  postbox: {
    component: <Postbox />,
    icon: 'edit' as IconName,
    name: 'Postbox',
    route: '/postbox',
    topLevelPageId: TopLevelPageId.postbox,
  },
  scanner: {
    component: <ScannerView />,
    icon: 'volume-up' as IconName,
    name: 'Scanner',
    route: '/scanner',
    topLevelPageId: TopLevelPageId.scanner,
  },
  squawk: {
    component: <Squawk />,
    icon: 'volume-up' as IconName,
    name: 'Squawk',
    route: '/squawk',
    topLevelPageId: TopLevelPageId.squawk,
  },
};

export const topLevelPageOrder = Object.values(topLevelPagesById).sort((a, b) => sortByString(a.name, b.name));

const collectionGroupIds = selectCollectionGroupIds();

// A route handler that contains the entirety of the application.
const NavigationBar: FunctionComponent<Props> = ({ currentState, logout, username }) => (
  <Navbar>
    <Navbar.Group align={Alignment.LEFT}>
      <Link className="bz-logo" to="/" />
      <Navbar.Heading style={{ margin: '0 5px 0 8px' }}>
        <Link to="/">Newsdesk Tools</Link>
      </Navbar.Heading>
      <Navbar.Divider />
    </Navbar.Group>
    <Navbar.Group align={Alignment.LEFT}>
      {collectionGroupIds.map(collectionGroupId => {
        const collectionGroupPluralName = selectCollectionGroupPluralName(collectionGroupId);
        const collectionGroupIcon = selectCollectionGroupIcon(collectionGroupId);
        const collectionIds = selectCollectionIdsFromCollectionGroupId(collectionGroupId);
        const isDisabled = hasNoCollectionAccessSelector(collectionIds, currentState);
        return (
          <DropdownMenu
            icon={collectionGroupIcon}
            isDisabled={isDisabled}
            key={collectionGroupId}
            text={collectionGroupPluralName}
          >
            {collectionIds.map(collectionId => {
              const permissionResourceId = selectCollectionPermissionResourceId(collectionId);
              const collectionAccess = selectCollectionAccess(permissionResourceId)(currentState);
              if (collectionAccess !== PermissionGroup.noAccess) {
                return (
                  <MenuLink
                    icon={selectCollectionIcon(collectionId)}
                    key={collectionId}
                    text={selectCollectionName(collectionId)}
                    to={selectCollectionRoute(collectionId)}
                  />
                );
              }
              return null;
            })}
            {collectionGroupPluralName === 'Calendars' && (
              <MenuLink icon="phone" text="Transcript" to="/calendar/transcript" />
            )}
          </DropdownMenu>
        );
      })}

      {topLevelPageOrder.map(page => {
        const isAdmin = adminAccessSelector(currentState);
        if (!isAdmin && page.name === 'Chat') {
          return;
        }
        return (
          <Link className="pt-link" key={page.name} to={page.route}>
            <Icon icon={page.icon} style={{ marginRight: '0.35em' }} />
            {page.name}
          </Link>
        );
      })}
    </Navbar.Group>
    <Navbar.Group align={Alignment.RIGHT}>
      <Navbar.Divider />
      <DropdownMenu icon="user" position={Position.BOTTOM_RIGHT}>
        <MenuItem href="/" text={`Signed in as ${username}`} />
        <MenuDivider />
        <MenuItem icon="log-out" onClick={logout} text="Logout" />
      </DropdownMenu>
    </Navbar.Group>
  </Navbar>
);

const mapStateToProps = (state: RootState): ReduxState => ({
  currentState: state,
});

export default connect<ReduxState>(mapStateToProps)(NavigationBar);
