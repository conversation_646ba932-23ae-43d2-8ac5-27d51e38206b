import { AgGridReact } from '@ag-grid-community/react';
import React from 'react';

import { ModuleRegistry } from '@ag-grid-community/core';
import '@ag-grid-community/styles/ag-grid.css';
import '@ag-grid-community/styles/ag-theme-quartz.css';
import styled from '@benzinga/themetron';
import { useRef, useState } from 'react';

import { useScannerFeed } from './ScannerContext';

// eslint-disable-next-line @nx/enforce-module-boundaries
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { CsvExportModule } from '@ag-grid-community/csv-export';
import { ClipboardModule } from '@ag-grid-enterprise/clipboard';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { RangeSelectionModule } from '@ag-grid-enterprise/range-selection';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { SideBarModule } from '@ag-grid-enterprise/side-bar';

import Hooks from '@benzinga/hooks';
import { QuoteProtos } from '@benzinga/scanner-manager';
import { useTime } from '@benzinga/time-manager-hooks';
import { DateTime } from 'luxon';
import Split from 'react-split';

ModuleRegistry.registerModules([
  ClientSideRowModelModule,
  CsvExportModule,
  ClipboardModule,
  ColumnsToolPanelModule,
  ExcelExportModule,
  FiltersToolPanelModule,
  MenuModule,
  RangeSelectionModule,
  SetFilterModule,
  SideBarModule,
]);

const SimpleGrid = (props: {
  className?: any;
  paused?: boolean;
  widgetParams: any;
  setSelectedItems: any;
  config?: any;
  setWidgetParams: (params: any) => void;
}) => {
  const { setSelectedItems, widgetParams } = props;
  const feed = useScannerFeed();
  const gridApi = useRef<any>(null);
  const [rowData, setRowData] = useState<any[]>([]);
  const latestTimeConfig = useTime();
  const selectedRowIdsRef = useRef<Set<any>>(new Set()); // Track selected row IDs

  // Column definitions based on widgetParams
  const columnDefs = React.useMemo(() => {
    return widgetParams.parameters.gridLayout.columns.map(col => ({
      field: col.colId,
      filter: col?.filter || true,
      hide: col.hide,
      pinned: col.pinned,
      rowDrag: col.rowDrag,
      sortable: true,
      width: col.width,
    }));
  }, [widgetParams.parameters.gridLayout.columns]);

  const autoSizeAllColumns = params => {
    const allColumnIds = params.columnApi.getAllDisplayedColumns().map(col => col.getColId());
    params.columnApi.autoSizeColumns(allColumnIds);
  };

  const formatRowData = React.useCallback(
    (quotes: QuoteProtos.IQuote[]) => {
      return quotes.map(quote => {
        const formattedQuote: Record<string, any> = {};
        for (const key in quote) {
          if (key.toLowerCase().includes('time')) {
            const rawValue = quote[key];
            if (rawValue) {
              const dateTime = DateTime.fromFormat(String(rawValue), 'yyyyMMddHHmmss', { zone: 'America/New_York' });
              if (dateTime.isValid) {
                const localDateTime = dateTime.setZone(latestTimeConfig.timezone);
                formattedQuote[key] =
                  latestTimeConfig.timeFormat === 'AM/PM'
                    ? localDateTime.toFormat('MMM d, yyyy h:mm a')
                    : localDateTime.toFormat('MMM d, yyyy HH:mm');
                continue;
              }
            }
          }
          formattedQuote[key] = quote[key];
        }
        return formattedQuote;
      });
    },
    [latestTimeConfig.timeFormat, latestTimeConfig.timezone],
  );

  Hooks.useSubscriber(props.paused ? undefined : feed, event => {
    switch (event.type) {
      case 'data_update': {
        const formattedData = formatRowData(event.rows);
        gridApi.current.setRowData(formattedData);
        // Reapply selection after rowData update
        if (gridApi.current) {
          const selectedIds = selectedRowIdsRef.current;
          gridApi.current.forEachNode(node => {
            if (selectedIds.has(node.data.symbol)) {
              node.setSelected(true);
              setSelectedItems(items => [...items, node.data]);
            }
          });
        }
        break;
      }
    }
  });

  const onSelectionChanged = () => {
    if (gridApi.current) {
      const selectedNodes = gridApi.current.getSelectedNodes();
      const selectedData = selectedNodes.map(node => node.data);
      const selectedIds = new Set(selectedData.map(data => data.symbol));
      selectedRowIdsRef.current = selectedIds; // Save selected IDs
      setSelectedItems(selectedData);
    }
  };

  const deselectAll = React.useCallback(() => {
    if (gridApi.current) {
      gridApi.current.deselectAll();
      gridApi.current.clearRangeSelection();
      selectedRowIdsRef.current.clear(); // Clear selected IDs
    }
  }, []);

  const handleClickOff = React.useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if ((event.target as HTMLDivElement)?.className?.includes('ag-body-viewport')) {
        deselectAll();
      }
    },
    [deselectAll],
  );

  return (
    <AgGridTheme className={'ag-theme-quartz'} onClick={handleClickOff}>
      <AgGridReact
        animateRows={true}
        columnDefs={columnDefs}
        defaultColDef={{
          filter: true,
        }}
        gridOptions={{
          animateRows: true,
          defaultColDef: {},
          enableRangeSelection: true,
          headerHeight: 30,
          loadingOverlayComponentParams: { loadingMessage: 'Waiting for Real-Time Data...' },
          rowHeight: 30,
        }}
        onFirstDataRendered={autoSizeAllColumns}
        onGridReady={params => (gridApi.current = params.api)}
        onSelectionChanged={onSelectionChanged}
        pagination={true}
        paginationPageSize={widgetParams.parameters.widgetScan.limit}
        rowData={rowData}
        rowDragManaged={true}
        rowSelection="multiple"
      />
    </AgGridTheme>
  );
};

export default SimpleGrid;

const AgGridTheme = styled.div`
  height: 100%;
  width: 100%;
`;
const StyledSplit = styled(Split)`
  flex-grow: 100;
  flex-shrink: 100;
  height: 600px;

  .gutter {
    cursor: ns-resize;
  }
`;
