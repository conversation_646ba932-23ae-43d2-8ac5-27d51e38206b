'use client';
import { DEFAULT_SCANNER_CONFIG, ScannerConfig } from '@benzinga/scanner-config-manager';
import { Scanner<PERSON>eed, ScannerManager } from '@benzinga/scanner-manager';
import { useScannerFilterableDefs } from '@benzinga/scanner-manager-hooks';
import { SessionContext } from '@benzinga/session-context';
import React from 'react';

export const ScannerFeedContext = React.createContext<ScannerFeed | undefined>(undefined);

export const ScannerFeedContextProvider: React.FC<React.PropsWithChildren<{ config: ScannerConfig }>> = props => {
  const session = React.useContext(SessionContext);
  const filtersDef = useScannerFilterableDefs();

  const config = React.useMemo<ScannerConfig>(
    () => ({
      ...props.config,
      filters: props.config.filters.map(
        filter =>
          filtersDef?.find(def => def.name === filter.field)?.filterConvert?.(filter, session.getSession()) ?? filter,
      ),
    }),
    [filtersDef, props.config, session],
  );
  const feed = session.getManager(ScannerManager).getFeed(config);
  return <ScannerFeedContext.Provider value={feed}> {props.children} </ScannerFeedContext.Provider>;
};

export const useScannerFeed = () => {
  const config = React.useContext(ScannerFeedContext);
  const session = React.useContext(SessionContext);
  return config ?? session.getManager(ScannerManager).getFeed(DEFAULT_SCANNER_CONFIG);
};
