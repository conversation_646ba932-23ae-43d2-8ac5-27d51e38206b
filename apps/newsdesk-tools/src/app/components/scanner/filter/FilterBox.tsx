'use client';
import { FilterObject, FullDataField } from '@benzinga/quotes-v3-fields-manager';
import { Permission } from '@benzinga/session';
import { Lock } from '@benzinga/themed-icons';
import styled, { css } from '@benzinga/themetron';
import { Input, Select, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import { RangeInput } from './RangeInput';
import SelectFilter from './SelectFilter';
// import WatchlistFilter from './WatchlistFilter';

export const isRangeFormat = (field?: FullDataField) => {
  switch (field?.format) {
    case 'calculatedChangePercent':
    case 'change':
    case 'changePercent':
    case 'changeShortenedNumber':
    case 'commaSeparatedNumber':
    case 'date':
    case 'dateTime':
    case 'number':
    case 'positiveNumber':
    case 'percentNumber':
    case 'positivePercentNumber':
    case 'positivePrice':
    case 'price':
    case 'shortenedNumber':
    case 'time':
      return true;
    case 'boolean':
    case 'flag':
    case 'link':
    case 'note':
    case 'period':
    case 'priceAlert':
    case 'progress':
    case 'set':
    case 'sparkLine':
    case 'symbol':
    case 'text':
    default:
      return false;
  }
};

export const minValue = (field?: FullDataField) => {
  switch (field?.format) {
    case 'positivePercentNumber':
    case 'positivePrice':
      return '0';
    case 'calculatedChangePercent':
    case 'change':
    case 'changePercent':
    case 'changeShortenedNumber':
    case 'commaSeparatedNumber':
    case 'date':
    case 'dateTime':
    case 'number':
    case 'positiveNumber':
    case 'percentNumber':
    case 'price':
    case 'shortenedNumber':
    case 'time':
    case 'boolean':
    case 'flag':
    case 'link':
    case 'note':
    case 'period':
    case 'priceAlert':
    case 'progress':
    case 'set':
    case 'sparkLine':
    case 'symbol':
    case 'text':
    default:
      return '';
  }
};

export const maxValue = (field?: FullDataField) => {
  switch (field?.format) {
    case 'positivePercentNumber':
    case 'positivePrice':
    case 'calculatedChangePercent':
    case 'change':
    case 'changePercent':
    case 'changeShortenedNumber':
    case 'commaSeparatedNumber':
    case 'date':
    case 'dateTime':
    case 'number':
    case 'positiveNumber':
    case 'percentNumber':
    case 'price':
    case 'shortenedNumber':
    case 'time':
    case 'boolean':
    case 'flag':
    case 'link':
    case 'note':
    case 'period':
    case 'priceAlert':
    case 'progress':
    case 'set':
    case 'sparkLine':
    case 'symbol':
    case 'text':
    default:
      return '';
  }
};

function f(field: FullDataField, filter?: FilterObject): FilterObject {
  if (!filter) {
    if (isRangeFormat(field)) {
      return { field: field.name ?? '', operator: 'bt', parameters: [] };
    } else {
      return { field: field.name ?? '', operator: 'in', parameters: [] };
    }
  }

  return filter;
}

const getFilterComponent = (field: FullDataField, filter: FilterObject, onChange: (filter: FilterObject) => void) => {
  switch (field.format) {
    case 'boolean':
      return <SelectFilter filter={filter} multiple={false} onChange={onChange} />;
    case 'text':
    case 'flag':
    case 'link':
    case 'note':
    case 'period':
      if (field.filterOptions) {
        return <SelectFilter filter={filter} multiple={true} onChange={onChange} />;
      } else {
        const before = (
          <StyledSelect dropdownMatchSelectWidth={false}>
            <Select.Option value="contains">Contains</Select.Option>
            <Select.Option value="equals">Equals</Select.Option>
            <Select.Option value="startsWith">Begins with</Select.Option>
            <Select.Option value="endsWith">Ends with</Select.Option>
          </StyledSelect>
        );
        return <Input addonBefore={before} size="small" />;
      }

    case 'calculatedChangePercent':
    case 'change':
    case 'changePercent':
    case 'changeShortenedNumber':
    case 'commaSeparatedNumber':
    case 'date':
    case 'dateTime':
    case 'number':
    case 'positiveNumber':
    case 'percentNumber':
    case 'positivePercentNumber':
    case 'positivePrice':
    case 'price':
    case 'shortenedNumber':
    case 'time':
      return <RangeInput filter={filter} onChange={onChange} />;
    // case 'watchlist':
    //   return <WatchlistFilter filter={filter} onChange={onChange} />;

    default:
      return <div />;
  }
};

/**
 * A component that contains a labels, tool icons (top right), and the field specific input.
 */
export const FilterBox: React.FC<{
  dataField: FullDataField;
  filter?: FilterObject;
  isMillerColumns?: boolean;
  isSingleNewsfeedField?: boolean;
  onChange: (filter: FilterObject) => void;
  permission?: Permission;
  tools?: (null | React.ReactElement)[];
}> = props => {
  const field = props.dataField;
  const [filter, setFilter] = useState(() => f(field, props.filter));
  const input = getFilterComponent(field, filter, props.onChange);

  useEffect(() => {
    setFilter(f(field, props.filter));
  }, [field, props.filter]);

  const activeClass = props.filter?.parameters && props.filter.parameters.length > 0 ? 'active' : '';
  return (
    <Container className={activeClass} isMillerColumn={props.isMillerColumns} key={field.name as string}>
      {props.isSingleNewsfeedField && (
        <Label>
          <Qtip
            overlayStyle={{ opacity: '1' }}
            title={
              <TipInfo>
                <b>{field.label}</b>

                {/* {!access && <Locked />} */}
                <p>{field.description}</p>
              </TipInfo>
            }
          >
            <LabelText>
              <Text>{field.label}</Text>
            </LabelText>
          </Qtip>

          <StyledInput>{input}</StyledInput>
          <Tools>{props.tools}</Tools>
        </Label>
      )}
    </Container>
  );
};

const StyledInput = styled.div`
  width: 100%;
`;

const Text = styled.div`
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const TipInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const LabelText = styled.div`
  color: ${props => props.theme.colors.foreground};
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  width: 150px;
  margin-right: 1em;
`;

const Label = styled.div`
  min-width: 100px;
  line-height: 20px;
  padding-right: 2px;
  text-align: left;
  vertical-align: middle;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 1em;
`;

export const Qtip = styled(Tooltip)`
  padding-left: 1px;
  &:hover {
    cursor: pointer;
  }
`;

export const Tools = styled.div`
  display: flex;
  gap: 4px;
  & > div {
    text-align: right;
  }
  & > div:hover {
    cursor: pointer;
  }
`;

const Container = styled.div<{ isMillerColumn?: boolean; isActive?: boolean }>`
  flex: 1;
  padding: 3px;
  margin-bottom: 8px;

  ${props =>
    props.isActive
      ? css`
          background: ${props => props.theme.colors.brandMuted}66;
        `
      : ''}

  ${Label} {
    ${props =>
      props.isMillerColumn
        ? css`
            min-width 20% !important
            margin-right 10px

            @media (max-width: 1100px)
              text-align left !important
        `
        : ''}
  }
`;

const StyledSelect = styled(Select)`
  .ant-select-selector {
    width: unset !important;
  }
`;

const Locked = styled(Lock)`
  fill: ${props => props.theme.colors.accent};
  font-size: 12px;
`;
