'use client';
import { FilterObject } from '@benzinga/quotes-v3-fields-manager';
import { ScannerConfig } from '@benzinga/scanner-config-manager';
import styled, { css } from '@benzinga/themetron';
import React from 'react';

import { Icon } from '@benzinga/core-ui';
import { faChevronDown } from '@fortawesome/pro-solid-svg-icons/faChevronDown';
import { faChevronRight } from '@fortawesome/pro-solid-svg-icons/faChevronRight';
import { FiltersBarSummary } from './FiltersBarSummary';
import { FiltersPanel as ScannerFilterPanel } from './FiltersPanel';

// For each filter options, make a set of parameter so that we can quickly check if a range
// is custom - and thus show a select box vs custom

export const FiltersBar: React.FC<{
  collapsed: boolean;
  setCollapsed: (collapsed: boolean) => void;
  onFiltersChanged: (filters: FilterObject[]) => void;
  onLoadConfig: (scannerConfig: ScannerConfig) => void;
  widgetParams: any;
}> = React.memo(props => {
  const [selectedFilters, setSelectedFilters] = React.useState<FilterObject[]>([]);
  //  const widgetParams = useWidgetParameters(ScannerWidgetManifest);

  const { collapsed, setCollapsed, widgetParams } = props;
  const config = widgetParams.parameters.widgetScan;
  const onFiltersChanged = props.onFiltersChanged;

  // Called when a filter is change from the filters bar, or child filter
  const updateFilters = React.useCallback(
    (filters: FilterObject[]) => {
      setSelectedFilters(filters);
      onFiltersChanged(filters);
    },
    [onFiltersChanged],
  );

  React.useEffect(() => {
    const newFilters = config.filters.map(filter => {
      if (filter.field === 'symbol' && filter.parameters[0]?.startsWith('watchlistId:')) {
        filter = { field: 'watchlist' ?? '', operator: 'in', parameters: filter.parameters };
      }
      return filter;
    });
    setSelectedFilters(newFilters);
    // setColumnedFilters(config.columns);
  }, [config.columns, config.filters]);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  return (
    <Div isOpen={collapsed}>
      <FiltersSummary onClick={toggleCollapsed}>
        <Arrow>
          <StyledIcon className="filter_summary" icon={collapsed ? faChevronRight : faChevronDown} />
          <StyledFilterTest>Filters:</StyledFilterTest>
        </Arrow>
        <FiltersBarSummary filters={selectedFilters} />
      </FiltersSummary>
      <StyledFilterHolder collapsed={collapsed}>
        <StyledScannerFilterPanel onFiltersChanged={updateFilters} selectedFilters={selectedFilters} />
      </StyledFilterHolder>
    </Div>
  );
});

const Div = styled.div<{ isOpen?: boolean; isScrollable?: boolean }>`
  ${props =>
    props.isOpen
      ? css`
          color: ${props => props.theme.colors.foreground};
          border: 1px solid ${props => props.theme.colors.brandMuted};
          background-color: ${props => props.theme.colors.backgroundActive}88;
          ${FiltersSummary} {
            background: ${props => props.theme.colors.brandMuted}44;
          }
        `
      : css`
          border: 1px solid ${props => props.theme.colors.background};
          border-left: 2px solid ${props => props.theme.colors.brandMuted};
          background-color: ${props => props.theme.colors.backgroundActive}88;

          :hover {
            color: ${props => props.theme.colors.brand};
            background: ${props => props.theme.colors.backgroundActive}88;
          }
        `}
  /* max-height: 100%; */
  // overflow: hidden;
  display: flex;
  flex-direction: column;

  .ant-tabs-content {
    flex-wrap: wrap !important;
    max-height: 200px !important;
    overflow: hidden !important;
  }

  .ant-tabs-tabpane {
    overflow: hidden !important;
  }

  .ant-tabs-content {
    overflow: auto !important;
  }
  .ant-tabs-tabpane {
    overflow: auto !important;
  }
`;

const FiltersSummary = styled.div`
  display: flex;
  align-items: center;
  .clear-filters {
    font-weight: bold;
    margin-right: 10px;
    margin-left: 10px;
    border: 1px solid;
    padding: 4px;
    margin-top: -3px;
  }
  &:hover {
    cursor: pointer;
  }
`;

const Arrow = styled.div`
  margin-top: auto;
  line-height: 26px;
  white-space: nowrap;
  margin-left: 6px;
  display: flex;
  svg {
    font-size: 10px;
  }
`;

const StyledIcon = styled(Icon)`
  margin-right: 4px;
  font-size: 12px !important;
`;

const StyledFilterTest = styled.span`
  margin-right: 4px;
`;

const StyledFilterHolder = styled.div<{ collapsed: boolean }>`
  display: ${props => (props.collapsed ? 'none' : 'block')};
  flex: 1;
  overflow: hidden;
`;

const StyledScannerFilterPanel = styled(ScannerFilterPanel)`
  height: 100%;
  padding: 12px;
  min-width: fit-content;
  width: 100%;
  overflow: hidden;
`;

export default FiltersBar;
