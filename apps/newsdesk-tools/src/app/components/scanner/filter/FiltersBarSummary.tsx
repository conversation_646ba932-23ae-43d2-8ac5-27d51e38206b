'use client';
import { Icon } from '@benzinga/core-ui';
import { FilterObject, FullDataField, QuotesV3FieldsManager } from '@benzinga/quotes-v3-fields-manager';
import { SessionContext } from '@benzinga/session-context';
import styled from '@benzinga/themetron';
import { faSquareXmark } from '@fortawesome/pro-regular-svg-icons/faSquareXmark';
import React, { useMemo } from 'react';
import { isRangeFormat } from './FilterBox';
import { fromShorthand, toShorthand } from './RangeInput';

export const FiltersBarSummary: React.FC<{
  filters: FilterObject[];
  onRemove?: (filter: FilterObject) => void;
}> = props => {
  return (
    <Div>
      <div>
        {props.filters.length === 0 && <FilterItemNone>No filters selected</FilterItemNone>}
        {props.filters.map(filter => (
          <FilterItem filter={filter} key={filter.field} onRemove={props.onRemove} />
        ))}
      </div>
    </Div>
  );
};

const RangeItem: React.FC<{
  field: FullDataField;
  filter: FilterObject;
}> = ({ field, filter }) => {
  const { parameters } = filter;

  const min = toShorthand(parameters ? parseFloat(parameters[0]) : undefined);
  const max = toShorthand(parameters ? parseFloat(parameters[1]) : undefined);
  const minN = fromShorthand(parameters[0]);
  if (minN && minN < 0 && parameters[1] === '') {
    return (
      <>
        {field.label} <Value>&lt; {min}</Value>
      </>
    );
  } else if (parameters[1] === '' || parameters[1] === null || parameters[1] === '0') {
    const pct = field.format.toLowerCase().includes('percentage') === true ? '%' : '';
    return (
      <>
        {field.label}{' '}
        <Value>
          &gt; {min}
          {pct}
        </Value>
      </>
    );
  } else if (parameters[0] === '') {
    return (
      <>
        {field.label} <Value>&lt; {max}</Value>
      </>
    );
  } else {
    return (
      <>
        {field.label}{' '}
        <Value>
          {min} to {max}
        </Value>
      </>
    );
  }
};

const GreaterOrLessThan: React.FC<{
  field: FullDataField;
  filter: FilterObject;
}> = ({ field, filter }) => {
  const { parameters } = filter;
  const value = toShorthand(parameters ? parseFloat(parameters[0]) : undefined);
  const pct = field.format.toLowerCase().includes('percentage') === true ? '%' : '';
  return (
    <>
      {field.label}{' '}
      <Value>
        {filter.operator} {value}
        {pct}
      </Value>
    </>
  );
};

const FilterItem: React.FC<{
  filter: FilterObject;
  onRemove?: (filter: FilterObject) => void;
}> = ({ filter, onRemove }) => {
  const field = React.useContext(SessionContext).getManager(QuotesV3FieldsManager).getScannerDefFromId(filter.field);

  const handleRemoveClick = useMemo(() => {
    return onRemove
      ? (ev: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
          ev.stopPropagation();
          onRemove(filter);
        }
      : undefined;
  }, [filter, onRemove]);

  if (field === undefined || filter.parameters.length === 0) {
    return null;
  }
  let params = filter.parameters;

  const content = () => {
    if (filter.operator === '>' || filter.operator === '<') {
      return <GreaterOrLessThan field={field} filter={filter} />;
    }

    if (isRangeFormat(field)) {
      return <RangeItem field={field} filter={filter} />;
    } else {
      switch (field.format) {
        case 'text': {
          // If we have options, use the label
          const options = field.filterOptions;
          if (options) {
            params = params
              .flatMap(param => {
                const k = Object.entries(options)?.find(([_, value]) => value === param);
                return k ? [k[0]] : [];
              })
              .filter(([_, value]) => value !== undefined && value !== null)
              .map(([key]) => key as string);
          }
          return (
            <>
              {field.label}: <Value>{params.join(', ')}</Value>
            </>
          );
        }
        default:
          return (
            <>
              {field.label}: <Value>{params.join(', ')}</Value>
            </>
          );
      }
    }
  };

  return (
    <FilterItemStyler>
      <FiContent>{content()}</FiContent>
      {handleRemoveClick && (
        <Remove onClick={handleRemoveClick}>
          <Icon className="slider-icon" icon={faSquareXmark} />
        </Remove>
      )}
    </FilterItemStyler>
  );
};

const Div = styled.div`
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  margin: auto;
  .clear-filters:hover {
    cursor: pointer;
  }
`;

const FilterItemStyler = styled.div`
  margin-left: 2px;
  margin-right: 2px;
  border-radius: 5px;
  white-space: nowrap;
  display: inline-block;
`;

const FiContent = styled.div`
  display: inline-block;
`;

const Remove = styled.div`
  margin-left: 4px;
  display: inline-block;
`;

const FilterItemNone = styled.b``;

const Value = styled.span`
  font-weight: bold;
`;
