'use client';
import React from 'react';

import { SessionContext } from '@benzinga/session-context';
import styled, { TC } from '@benzinga/themetron';

import { FilterObject, QuotesV3FieldsManager } from '@benzinga/quotes-v3-fields-manager';
import { Input, Radio, RadioChangeEvent, Slider } from 'antd';
import { maxValue, minValue } from './FilterBox';

const { Button: RadioButton, Group: RadioGroup } = Radio;

const { Group: InputGroup } = Input;

/**
 * If options is supplied, then a select is shown if the current value is either: 1) undefined, or 2) matches
 * the (human format) value.
 */
export const RangeInput: React.FC<{
  filter: FilterObject;
  onChange: (filter: FilterObject) => void;
  onOpenChanged?: (isOpen: boolean) => void;
}> = props => {
  const session = React.useContext(SessionContext);
  const field = React.useMemo(
    () => session.getManager(QuotesV3FieldsManager).getScannerDefFromId(props.filter.field),
    [props.filter.field, session],
  );
  const [custom, setCustom] = React.useState(() => !field?.filterOptions);
  const RadioOptions = React.useMemo(() => {
    const inputOptions = [
      { id: false, label: 'Slider' },
      { id: true, label: 'Custom' },
    ];
    return inputOptions.map(({ id, label }) => (
      <RadioButton key={id.toString()} value={label}>
        {label}
      </RadioButton>
    ));
  }, []);

  const paramToValue = React.useCallback(
    (params: string[]) =>
      params?.flatMap<string>(d => {
        switch (d) {
          case '':
          case '__CUSTOM':
            return [];
          case '\u003e':
            return [maxValue(field) || 'Infinity'];
          case '\u003c':
            return [minValue(field) || '-Infinity'];
          default:
            return [toShorthand(fromShorthand(d))];
        }
      }) ?? [],
    [field],
  );

  const marks = React.useMemo(() => {
    if (!field?.filterOptions) return { 0: '0', 1: 'Infinity' };

    const values = new Set(
      Object.values(field.filterOptions)
        .flat()
        .map(value => {
          if (!value) return null;
          if (value === '\u003e') return maxValue(field) || 'Infinity';
          if (value === '\u003c') return minValue(field) || '-Infinity';
          return toShorthand(fromShorthand(String(value)));
        })
        .filter(Boolean),
    );

    return Array.from(values)
      .sort((a, b) =>
        a === '-Infinity' || b === 'Infinity'
          ? -1
          : a === 'Infinity' || b === '-Infinity'
            ? 1
            : Number(fromShorthand(a ?? '')) - Number(fromShorthand(b ?? '')),
      )
      .reduce((acc, value, index) => ({ ...acc, [index]: value }), {});
  }, [field]);

  const params = React.useRef(props.filter.parameters);
  params.current = props.filter.parameters ?? [minValue(field), maxValue(field)];

  const onMinChange: React.ChangeEventHandler<HTMLInputElement> = React.useCallback(e => {
    params.current = [`${fromShorthand(e.target.value) ?? ''}`, params.current[1] ?? ''];
  }, []);

  const onMaxChange: React.ChangeEventHandler<HTMLInputElement> = React.useCallback(e => {
    params.current = [params.current[0] ?? '', `${fromShorthand(e.target.value) ?? ''}`];
  }, []);

  const onBlur = React.useCallback(() => {
    const onChange = props.onChange;
    onChange({ ...props.filter, parameters: params.current });
  }, [props.filter, props.onChange]);

  const onParametersChange = React.useCallback(
    (parameters: number[]) => {
      const onChange = props.onChange;
      onChange({
        ...props.filter,
        parameters: parameters.map(a => {
          const val = marks[a];
          switch (val) {
            case 'Infinity':
            case '-Infinity':
              return '';
            default:
              return fromShorthand(val)?.toString() ?? '';
          }
        }),
      });
    },
    [marks, props.filter, props.onChange],
  );
  const onKeyDown = React.useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === ' ') {
      e.preventDefault();
    }
  }, []);

  const handleCustomChange = React.useCallback((event: RadioChangeEvent) => {
    setCustom(event.target.value === 'Custom');
  }, []);

  if (!field) {
    return null;
  }

  const renderCustomFloatFilter = () => {
    return (
      <InputGroup className="movers-input-group" compact style={{ alignItems: 'center', display: 'flex' }}>
        <Input
          addonBefore="Min"
          defaultValue={params.current[0]}
          onBlur={onBlur}
          onChange={onMinChange}
          onKeyDown={onKeyDown}
          placeholder="0"
          size="small"
          style={{ marginRight: '16px' }}
        />

        <Input
          addonBefore="Max"
          defaultValue={params.current[1]}
          onBlur={onBlur}
          onChange={onMaxChange}
          onKeyDown={onKeyDown}
          placeholder="Infinity"
          size="small"
        />
      </InputGroup>
    );
  };

  const renderBaseFloatFilter = () => (
    <SliderContainer>
      <Slider
        defaultValue={[
          (Object.entries(marks).find(d => d[1] === (minValue(field) || '-Infinity'))?.[0] ?? 0) as number,
          (Object.entries(marks).find(d => d[1] === (maxValue(field) || 'Infinity'))?.[0] ?? 0) as number,
        ]}
        dots
        marks={marks}
        max={Object.keys(marks).length - 1}
        min={0}
        onChange={onParametersChange as any}
        range
        step={1}
        style={{ marginBottom: '14px', marginLeft: '6px', marginRight: '6px', marginTop: '0px' }}
        tooltip={{ formatter: null }}
        value={[
          (Object.entries(marks).find(
            d => d[1] === (paramToValue([props.filter.parameters[0] ?? minValue(field)])[0] ?? '-Infinity'),
          )?.[0] ?? 0) as number,
          (Object.entries(marks).find(
            d => d[1] === (paramToValue([props.filter.parameters[1] ?? maxValue(field)])[0] ?? 'Infinity'),
          )?.[0] ?? 0) as number,
        ]}
      />
    </SliderContainer>
  );

  return (
    <Parameter>
      <ColumnStyles>
        <div>{custom ? renderCustomFloatFilter() : renderBaseFloatFilter()}</div>

        <RadioGroup buttonStyle="solid" onChange={handleCustomChange} size="small" value={custom ? 'Custom' : 'Slider'}>
          {RadioOptions}
        </RadioGroup>
      </ColumnStyles>
    </Parameter>
  );
};

export const Parameter = styled(TC.Row)`
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  padding: 0em 0em;
`;

export const ParameterLabel = styled(TC.Row)`
  align-items: center;
  display: inline-flex;
  margin: 0.5em 0;
  min-width: 100px;
  white-space: nowrap;
`;

export const ColumnStyles = styled(TC.Column)`
  align-items: center;
  display: grid;
  grid-gap: 16px;
  grid-template-columns: auto 106px;
  width: 100%;
  margin-bottom: 2px;
`;

export const SliderContainer = styled(TC.Inline)`
  flex-grow: 1;
  height: 30px;
  margin-top: -20px;
  /* max-width: 300px; */
  width: 100%;
`;

export function fromShorthand(shorthand: string): number | undefined {
  if (shorthand === '') {
    return undefined;
  } else if (shorthand === 'Infinity') {
    return Infinity;
  } else if (shorthand === '-Infinity') {
    return -Infinity;
  } else {
    // Clean the string up, remove commas and whitespace
    shorthand = shorthand.toLowerCase().replaceAll(/[, ]+/g, '');

    let ch = shorthand.length > 0 ? shorthand[shorthand.length - 1] : undefined;

    //if last character is not a digit, remove it for integer parsing
    if (ch != null && !(ch[0] >= '0' && ch[0] <= '9')) {
      shorthand = shorthand.substring(0, shorthand.length - 1);
    } else if (ch != null && ch[0] >= '0' && ch[0] <= '9') {
      ch = undefined;
    }

    //strict number conversion from string
    const value = Number(shorthand);

    if (isNaN(value)) return NaN;
    switch (ch) {
      case 'b':
        return value * 1e9;
      case 't':
        return value * 1e12;
      case 'm':
        return value * 1e6;
      case 'k':
        return value * 1e3;
      case undefined:
        return value;
      default:
        return NaN;
    }
  }
}

/**
 * Converts a number to shorthand string, i.e., 1000 -> 1K
 */
export const toShorthand = (value: number | undefined): string => {
  if (value === undefined) return '';
  switch (true) {
    case value === 0:
      return '0';
    case value === Infinity:
      return 'Infinity';
    case value === -Infinity:
      return '-Infinity';
    case String(value) === '<' || String(value) === '>':
      return String(value);
    case !value:
      return '';
    case value < 1e3:
      return String(value);
    case value < 1e6:
      return toString(value / 1e3, 'K');
    case value < 1e9:
      return toString(value / 1e6, 'M');
    case value < 1e12:
      return toString(value / 1e9, 'B');
    default:
      return toString(value / 1e12, 'T');
  }
};

const toString = (value: number, suffix: string) =>
  `${value
    .toFixed(2)
    .toString()
    .replaceAll(/\.?0+$/g, '')}${suffix}`;
