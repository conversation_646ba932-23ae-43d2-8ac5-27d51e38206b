'use client';
import React from 'react';

import { Icon } from '@benzinga/core-ui';
import { DataField, FilterObject, QuotesV3FieldsManager } from '@benzinga/quotes-v3-fields-manager';
import { useScannerDefs } from '@benzinga/scanner-manager-hooks';
import { Permission } from '@benzinga/session';
import { SessionContext } from '@benzinga/session-context';
import { Close } from '@benzinga/themed-icons';
import styled from '@benzinga/themetron';
import { faSearch } from '@fortawesome/pro-regular-svg-icons';
import { faFilterSlash } from '@fortawesome/pro-solid-svg-icons';
import { Button, Input, Result, Tooltip } from 'antd';
import { FilterBox, Tools } from './FilterBox';
import { FilterSearch } from './FilterSearch';

/**
 * Component to list/search/select filters for the scanner
 */
export const FiltersPanel: React.FC<{
  className?: string;
  permission?: Permission;
  selectedFilters: FilterObject[];
  onFiltersChanged: (filters: FilterObject[]) => void;
}> = props => {
  const session = React.useContext(SessionContext);

  const [searchText, setSearchText] = React.useState<string>('');

  const updateFilters = (filters: FilterObject[]) => {
    props.onFiltersChanged(filters);
  };

  const handleRemove = (removedFilter: FilterObject) => {
    updateFilters(props.selectedFilters.filter(filter => filter.field !== removedFilter.field));
  };

  const removeAll = () => {
    updateFilters([]);
  };

  const defs = useScannerDefs();
  const dataFields = React.useMemo(() => defs?.filter(df => df.filterable) ?? [], [defs]);

  return (
    <FiltersContainer className={props.className}>
      <FiltersContainerRow>
        <Input allowClear onChange={ev => setSearchText(ev.target.value)} placeholder="Search.." size="middle" />
        <Tooltip placement="bottomRight" title="Search Filters">
          <StyledButton size="middle">
            <StyledIcon icon={faSearch} />
          </StyledButton>
        </Tooltip>
        <Tooltip placement="bottomRight" title="Clear Filters">
          <StyledButton disabled={props.selectedFilters.length === 0} onClick={removeAll} size="middle">
            <StyledIcon icon={faFilterSlash} />
          </StyledButton>
        </Tooltip>
      </FiltersContainerRow>
      <FiltersContainerRowBody>
        {dataFields.filter(field => field.label?.toLowerCase()?.includes(searchText.toLowerCase())).length > 0 && (
          <FilterSearchStyler>
            <StyledFilterSearch
              onAdd={filter => updateFilters([...props.selectedFilters, filter])}
              onRemove={removed =>
                updateFilters(props.selectedFilters.filter(filter => filter.field !== removed.field))
              }
              searchText={searchText}
              selectedFilters={props.selectedFilters}
            />
          </FilterSearchStyler>
        )}
        <Main>
          {dataFields.filter(field => field.label?.toLowerCase()?.includes(searchText.toLowerCase())).length === 0 ? (
            <Result title="No filters available with Search criteria." />
          ) : props.selectedFilters
              .map(filter => ({
                df: session.getManager(QuotesV3FieldsManager).getScannerDefFromId(filter.field),
                filter,
              }))
              .filter(
                (item): item is { df: DataField; filter: FilterObject } =>
                  !!item.df?.label?.toLowerCase().includes(searchText.toLowerCase()),
              ).length === 0 ? (
            <Result title="No filters selected. Add filters from the left." />
          ) : (
            props.selectedFilters
              .map(filter => ({
                df: session.getManager(QuotesV3FieldsManager).getScannerDefFromId(filter.field),
                filter,
              }))
              .filter(
                (item): item is { df: DataField; filter: FilterObject } =>
                  !!item.df?.label?.toLowerCase().includes(searchText.toLowerCase()),
              )
              .map(({ df, filter }) => (
                <FilterCt key={filter.field}>
                  <FilterBox
                    dataField={df}
                    filter={filter}
                    isSingleNewsfeedField={true}
                    onChange={filter => updateFilters(mergeFilter(filter, props.selectedFilters))}
                    permission={props.permission}
                    tools={[
                      <div className="remove" onClick={() => handleRemove(filter)}>
                        <CloseIcon />
                      </div>,
                    ]}
                  />
                </FilterCt>
              ))
          )}
        </Main>
      </FiltersContainerRowBody>
    </FiltersContainer>
  );
};

const CloseIcon = styled(Close)`
  fill: ${props => props.theme.colors.foregroundInactive} !important;
  &:hover {
    fill: ${props => props.theme.colors.danger} !important;
  }
`;

const StyledButton = styled(Button)`
  height: 34px;
  margin-left: -1px;

  &:hover {
    z-index: 1;
  }
`;

const StyledIcon = styled(Icon)`
  display: flex;
`;

const StyledFilterSearch = styled(FilterSearch)`
  border-right: solid 1px ${props => props.theme.colors.backgroundActive};
`;

const FiltersContainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const FiltersContainerRow = styled.div`
  display: flex;

  .anticon-search {
    color: ${props => props.theme.colors.foregroundInactive};
  }
`;

const FiltersContainerRowBody = styled(FiltersContainerRow)`
  padding-left: 4px;
  overflow: hidden;
`;

const FilterSearchStyler = styled.div`
  width: 210px;
`;

const Main = styled.div`
  margin-left: 5px;
  margin-top: 5px;
  width: 100%;
  min-width: 250px;
  overflow-y: auto;
`;

const FilterCt = styled.div`
  padding: 5px;
  display: flex;

  ${Tools} {
    line-height: 25px;
    padding-left: 2px;
  }
`;

function mergeFilter(filter: FilterObject, filters: FilterObject[]) {
  return filters.map(f => (f.field === filter.field ? filter : f));
}
