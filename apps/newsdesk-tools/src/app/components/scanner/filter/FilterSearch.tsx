'use client';
import { FilterObject, FullDataField } from '@benzinga/quotes-v3-fields-manager';
import { useScannerDefs } from '@benzinga/scanner-manager-hooks';
import styled from '@benzinga/themetron';
import { Checkbox } from 'antd';
import React from 'react';
import { isRangeFormat, maxValue, minValue, Qtip } from './FilterBox';

/**
 * Used for the dropdown for filters search. A list of filter names, with search functions.
 */
export const FilterSearch: React.FC<{
  className?: string;
  searchText?: string;
  onAdd: (filter: FilterObject) => void;
  onRemove: (filter: FilterObject) => void;
  selectedFilters: FilterObject[];
}> = props => {
  const selectedByName = React.useMemo(
    () => new Map(props.selectedFilters.map(filter => [filter.field, filter])),
    [props.selectedFilters],
  );

  const toggle = (dataField: FullDataField) => {
    if (!dataField.name) {
      return;
    }
    const filter = selectedByName.get(dataField.name);
    if (filter) {
      props.onRemove(filter);
    } else {
      const newFilter: FilterObject = {
        field: dataField.name,
        operator: 'bt',
        parameters: isRangeFormat(dataField) ? [minValue(dataField), maxValue(dataField)] : [],
      };
      props.onAdd(newFilter);
    }
  };
  const defs = useScannerDefs();
  const dataFields = React.useMemo(() => defs?.filter(df => df.filterable && df.category !== 'NONE') ?? [], [defs]);

  const result = React.useMemo(() => {
    return (dataFields ?? [])
      .filter(field => field.label?.toLowerCase().includes(props.searchText?.toLowerCase() ?? ''))
      .reduce<{ category: string; dataFields: FullDataField[] }[]>((acc, field) => {
        const cat = acc.find(a => a.category === field.category);

        if (cat) {
          cat.dataFields.push(field);
        } else {
          acc.push({ category: field.category, dataFields: [field] });
        }
        return acc;
      }, []);
  }, [dataFields, props.searchText]);

  return (
    <FilterSearchCt className={props.className}>
      <FiltersDiv>
        {result
          .filter(c => c.dataFields.length > 0)
          .map(c => (
            <div key={c.category}>
              <H2>{c.category}</H2>
              {c.dataFields.map(df => (
                <DataFieldItem df={df} key={df.name} selectedByName={selectedByName} toggle={toggle} />
              ))}
            </div>
          ))}
      </FiltersDiv>
    </FilterSearchCt>
  );
};

const DataFieldItem: React.FC<{
  df: FullDataField;
  selectedByName: Map<string, FilterObject>;
  toggle: (df: FullDataField) => void;
}> = props => {
  const df = props.df;

  return (
    <div>
      <CheckboxStyled checked={props.selectedByName.has(df.name ?? '')} key={df.name} onChange={() => props.toggle(df)}>
        <Qtip
          overlayStyle={{ opacity: '1' }}
          placement="right"
          title={
            <TipInfo>
              <b>{df.label}</b>
              <p>{df.description}</p>
            </TipInfo>
          }
        >
          <LabelText>
            <Label>{df.label}</Label>
          </LabelText>
        </Qtip>
      </CheckboxStyled>
    </div>
  );
};

const H2 = styled.h2`
  color: ${props => props.theme.colors.foreground};
`;

const TipInfo = styled.div`
  display: flex;
  flex-direction: column;
`;

const CheckboxStyled = styled(Checkbox)`
  .ant-checkbox + span {
    overflow: hidden !important;
  }

  width: 100%;
`;

const FiltersDiv = styled.div`
  overflow: auto;
  overflow-x: hidden;
  height: 100%;
  padding: 8px 0px;
`;

const LabelText = styled.div`
  white-space: nowrap;
  overflow: hidden;
  display: flex;
`;

const Label = styled.div`
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
`;

const FilterSearchCt = styled.div`
  height: 100%;
  display: flex;
  flex-direction: column;
  input {
    margin-bottom: 5px;
  }
  .title {
    font-size: larger;
    font-weight: bold;
  }
`;
