'use client';
import { CloseCircleOutlined } from '@ant-design/icons';
import styled, { css } from '@benzinga/themetron';
import React, { useState } from 'react';
import { FilterBox } from './FilterBox';

import { FilterObject, FullDataField } from '@benzinga/quotes-v3-fields-manager';
const Tool = styled.div<{ isRemove?: boolean }>`
  margin-left: 5px;
  ${props =>
    props.isRemove
      ? css`
          display: inline-block;
        `
      : ``}
`;

export const Filters: React.FC<{
  dataFields: FullDataField[];
  filters: FilterObject[];
  isMillerColumns?: boolean;
  onFiltersChanged: (filters: FilterObject[]) => void;
}> = props => {
  const [filters, setFilters] = useState(props.filters);

  const onFilterChanged = (filter: FilterObject) => {
    const newFilters = mergeFilter(filter, filters);
    setFilters(newFilters);
    props.onFiltersChanged(newFilters);
  };

  const handleRemove = React.useCallback(
    (removedFilter: FilterObject) => {
      const newFilters = filters.filter(filter => filter.field !== removedFilter.field);
      setFilters(newFilters);
      props.onFiltersChanged(newFilters);
    },
    [filters, props],
  );

  const filtersByName = new Map(props.filters.map(filter => [filter.field, filter]));

  const clearTool = (_: FullDataField, filter?: FilterObject) => {
    if (filter == null) return null;
    return (
      <Tool key="reset" onClick={() => handleRemove(filter)}>
        <CloseCircleOutlined />
      </Tool>
    );
  };

  return (
    <FiltersContainer>
      {props.dataFields
        // Hack, hide symbol filter for now
        .filter(df => df.name !== 'symbol')
        .map(df => (
          <FilterCt key={df.name}>
            <FilterBox
              dataField={df}
              filter={filtersByName.get(df.name ?? '')}
              isMillerColumns={props.isMillerColumns}
              isSingleNewsfeedField={props.dataFields?.length > 1}
              key={df.name ?? ('' as string)}
              onChange={onFilterChanged}
              tools={[clearTool(df, filtersByName.get(df.name ?? ''))]}
            />
          </FilterCt>
        ))}
      {/* For now, we need to check this hack if there is Adv Newsfeed filter or Scanner */}
      {props.dataFields?.length > 1 && <BottomText>* Updated intraday on each market tick.</BottomText>}
    </FiltersContainer>
  );
};

const BottomText = styled.div`
  margin: 20px 0px 0px 10px;
`;

const FiltersContainer = styled.div`
  flex: 1;
  margin-bottom: 0.5em;
  max-width: 100%;
`;

const FilterCt = styled.div`
  margin: 5px;
  position: relative;
  .remove {
    position: absolute;
    right: 0px;
    top: 50%;
    transform: translateY(-50%);
  }
  .remove:hover {
    cursor: pointer;
  }
`;

export function mergeFilter(filter: FilterObject, filters: FilterObject[]) {
  // Remove
  if (filter.parameters.length === 0 || (isEmpty(filter.parameters[0]) && isEmpty(filter.parameters[1]))) {
    return filters.filter(f => f.field !== filter.field);
    // Update
  } else if (filters.filter(f => f.field === filter.field)) {
    const filters2 = filters.filter(f => f.field !== filter.field);
    return [filter, ...filters2];
    // Add
  } else {
    return [filter, ...filters];
  }
}

function isEmpty(value: unknown) {
  if (value === null) {
    return true;
  }
  if (!value) {
    return true;
  }
  if (typeof value == 'number' && isNaN(value)) {
    return true;
  }
  return false;
}
