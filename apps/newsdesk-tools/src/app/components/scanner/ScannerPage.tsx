import { useAutoCompleteSymbols } from '@benzinga/pro-ui';

import { <PERSON><PERSON>, Modal } from 'antd';

import { News } from '../../reducers/entities/newsEntities';

import { noop } from 'ramda-adjunct';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import Split from 'react-split';
import styled from 'styled-components';
import { postNewsAsync } from '../../actions';
import {
  addPost,
  clearCalendarPost,
  loadCategories,
  removePost,
  setActivePost,
  setPosterField,
  setPosterFields,
} from '../../actions/news';
import { fetchNewsAsync } from '../../actions/postbox';
import { FetchNews } from '../../reducers/entities/postboxEntities';
import { RootState } from '../../state';
import { selectStory } from '../collections/collectionSelectors';
import Poster from '../postbox/Poster';
import FiltersBar from './filter/FiltersBar';
import { ScannerFeedContextProvider } from './ScannerContext';
import ScannerView from './ScannerView';
const WIDGET_PARAMS = {
  parameters: {
    filtersMenuCollapsed: false,
    flightMode: false,
    gridLayout: {
      bzColumnState: [],
      columns: [
        {
          colId: 'symbol',
          hide: false,
          pinned: 'left',
          rowDrag: true,
          width: 100,
        },
        {
          colId: 'name',
          hide: false,
          width: 100,
        },
        {
          colId: 'price',
          filter: 'agNumberColumnFilter',
          hide: false,
          width: 120,
        },
        {
          colId: 'change',
          filter: 'agNumberColumnFilter',
          hide: false,

          width: 120,
        },
        {
          colId: 'changePercent',
          filter: 'agNumberColumnFilter',

          hide: false,
          width: 100,
        },
        {
          colId: 'dayVolume',
          filter: 'agNumberColumnFilter',

          hide: false,
          width: 120,
        },
        {
          colId: 'shareFloat',
          filter: 'agNumberColumnFilter',

          hide: false,
          width: 120,
        },
        {
          colId: 'sharesShortPercentOfFloat',
          filter: 'agNumberColumnFilter',

          hide: false,
          width: 120,
        },
        {
          colId: 'sharesOutstanding',
          filter: 'agNumberColumnFilter',

          hide: false,
          width: 120,
        },
        {
          colId: 'marketCap',
          filter: 'agNumberColumnFilter',

          hide: false,
          width: 120,
        },
        {
          colId: 'gicsSectorName',
          hide: false,
          width: 120,
        },
        {
          colId: 'gicsIndustryName',
          hide: false,
          width: 120,
        },
      ],
      columnsResized: false,
      filter: {},
    },
    isPopout: false,
    sendGroup: null,
    tags: [],
    widgetScan: {
      filters: [
        {
          field: 'subtype',
          operator: 'in',
          parameters: ['ADR', 'COMMON_SHARE', 'ETF'],
        },
      ],
      limit: 100,
      refreshInterval: -2,
      sortDir: 1,
      sortField: 'changePercent',
      source: 'stocks',
      tableParameters: {
        columns: [
          { colId: 'symbol', name: 'symbol', width: 100 },
          { colId: 'name', name: 'name', width: 100 },
          { colId: 'price', name: 'price', width: 120 },
          { colId: 'change', name: 'change', width: 120 },
          { colId: 'changePercent', name: 'changePercent', width: 100 },
          { colId: 'dayVolume', name: 'dayVolume', width: 100 },
          { colId: 'shareFloat', name: 'shareFloat', width: 100 },
          { colId: 'sharesShortPercentOfFloat', name: 'sharesShortPercentOfFloat', width: 100 },
          { colId: 'sharesOutstanding', name: 'sharesOutstanding', width: 100 },
          { colId: 'marketCap', name: 'marketCap', width: 100 },
          { colId: 'gicsSectorName', name: 'gicsSectorName', width: 100 },
          { colId: 'gicsIndustryName', name: 'gicsIndustryName', width: 100 },
        ],
      },
    },
  },
};

function ScannerPage(props) {
  const [widgetParams, setWidgetParams] = useState(() => WIDGET_PARAMS);
  const [openPostForm, setOpenPostForm] = useState(false);
  const { postNewsAsync, setPosterField, story, ...nprops } = props;
  const [filtersMenuCollapsed, setFiltersMenuCollapsed] = useState(true);
  // const activePost = find(story.posts, { postId: story.activePostId });

  const [selectedItems, setSelectedItems] = useState([]);
  useEffect(() => {
    if (selectedItems.length > 0) {
      setPosterField(
        'primaryTickers',
        selectedItems.map(item => ({
          data: { ...item },
          label: item.symbol,
          value: item.symbol,
        })),
      );
    }
  }, [setPosterField, selectedItems]);

  const symbols = useAutoCompleteSymbols(widgetParams?.parameters?.tags);
  const widgetScan = React.useMemo(() => {
    const widgetScan = widgetParams.parameters.widgetScan;
    return {
      ...widgetScan,
      filters: [...widgetScan.filters, { field: 'symbol', operator: 'in', parameters: symbols ?? [] }],
    };
  }, [symbols, widgetParams.parameters.widgetScan]);

  const handleFiltersChanged = filters => {
    setWidgetParams({
      ...widgetParams,
      parameters: {
        ...widgetParams.parameters,
        widgetScan: {
          ...widgetParams.parameters.widgetScan,
          filters,
        },
      },
    });
  };
  return (
    <ScannerStyler>
      <div
        style={{
          alignItems: 'center',
          display: 'flex',
          justifyContent: 'space-between',
          padding: '10px',
        }}
      >
        <Button disabled={selectedItems.length === 0} onClick={() => setOpenPostForm(true)}>
          Create WIIMS
        </Button>
      </div>
      {!filtersMenuCollapsed ? (
        <StyledSplit direction="vertical" gutterSize={6}>
          <FiltersBar
            collapsed={filtersMenuCollapsed}
            onFiltersChanged={handleFiltersChanged}
            onLoadConfig={props.onConfigChanged ? props?.onConfigChanged : noop}
            setCollapsed={setFiltersMenuCollapsed}
            widgetParams={widgetParams}
            // filters={config.filters}
          />
        </StyledSplit>
      ) : (
        <FiltersBar
          collapsed={filtersMenuCollapsed}
          onFiltersChanged={handleFiltersChanged}
          onLoadConfig={props.onConfigChanged ? props?.onConfigChanged : noop}
          setCollapsed={setFiltersMenuCollapsed}
          widgetParams={widgetParams}
          // filters={config.filters}
        />
      )}
      <div className="ag-theme-alpine" style={{ height: '500px', marginTop: '10px', width: '100%' }}>
        <ScannerFeedContextProvider config={widgetScan}>
          <ScannerView
            config={widgetParams.parameters.widgetScan}
            setSelectedItems={setSelectedItems}
            setWidgetParams={setWidgetParams}
            widgetParams={widgetParams}
          />
        </ScannerFeedContextProvider>
      </div>
      <div>
        <Modal footer={null} onCancel={() => setOpenPostForm(false)} open={openPostForm} width={'80%'}>
          <Poster {...nprops} />
        </Modal>
      </div>
    </ScannerStyler>
  );
}

const StyledSplit = styled(Split)`
  flex-grow: 100;
  flex-shrink: 100;
  height: 400px;
  margin-bottom: 10px;
  .gutter {
    background-color: #fff;
  }
`;
const ScannerStyler = styled.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;

  .ag-cell-label-container .number {
    text-align: right !important;
    overflow: hidden !important;
  }

  .ag-cell-label-container .string {
    text-align: left !important;
    overflow: hidden !important;
  }

  .ag-cell.number {
    text-align: right;
  }

  .sort-desc {
    color: #fff;
    background: #11463b;
  }

  .sort-asc {
    color: #fff;
    background: #4b1c19;
  }

  .sort-desc-abs {
    color: #fff;
    background: #141742;
  }
`;
const mapStateToProps = (state: RootState): ReduxState => ({
  postBoxStory: state.postBoxStory as FetchNews,
  story: selectStory(state),
});

const mapDispatchToProps: DispatchableActions = {
  addPost,
  clearCalendarPost,
  fetchNewsAsync,
  loadCategories,
  postNewsAsync,
  removePost,
  setActivePost,
  setPosterField,
  setPosterFields,
};

export default connect<ReduxState, DispatchableActions>(mapStateToProps, mapDispatchToProps)(ScannerPage);

interface ReduxState {
  story: News;
  postBoxStory: FetchNews;
}

interface DispatchableActions {
  addPost: typeof addPost;
  removePost: typeof removePost;
  clearCalendarPost: typeof clearCalendarPost;
  loadCategories: typeof loadCategories;
  postNewsAsync: typeof postNewsAsync;
  setActivePost: typeof setActivePost;
  setPosterField: typeof setPosterField;
  setPosterFields: typeof setPosterFields;
  fetchNewsAsync: typeof fetchNewsAsync;
}

interface State {
  activeKey: number;
}

type Props = ReduxState & DispatchableActions;
