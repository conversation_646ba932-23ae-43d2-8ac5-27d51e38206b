import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';

// Order of these two style imports matters
import '../env';
import './app/logic';
import './app/style.scss';

import { ThemeProvider } from '@benzinga/themetron';
import { createBrowserHistory, createMemoryHistory, History, MemoryHistory } from 'history';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { routes } from './app/routes';
import store from './app/store';

const App = () => {
  const historyRef = React.useRef<History | MemoryHistory>();
  if (historyRef.current == null) {
    historyRef.current = process.env.NODE_ENV !== 'test' ? createBrowserHistory() : createMemoryHistory();
  }

  const history = historyRef.current;
  const router = createBrowserRouter(routes);

  return (
    <ThemeProvider theme={'light'}>
      <Provider store={store}>
        <RouterProvider router={router} />
      </Provider>
    </ThemeProvider>
  );
};

const element = document.getElementById('root');
const root = createRoot(element as HTMLElement);
root.render(<App />);
